#include "bsp_rtc.h"

static RTCTypeDef gRTC = {
	RTC,RCU_RTC,
	{
		22,RTC_APR,0x08,RTC_FRIDAY,0x15,0x42,0x05,10,11,R<PERSON>_<PERSON>,RTC_24HOUR
	},
	{
		RTC_APR,0x08,RTC_FRIDAY,0x15,0x42,0x05,RTC_PM
	}
};
RTCTypeDef* pRTC = &gRTC;

TimeStampTypeDef gTimeStamp;

TimeStampTypeDef* pTimestamep = &gTimeStamp;

// Prescaler variables are no longer needed as they are set directly in rtc_setup()

void bsp_rtc_init(RTCTypeDef* prtc)
{
	/* enable access to RTC registers in Backup domain */
	rcu_periph_clock_enable(prtc->rtcClk);
	pmu_backup_write_enable();
	rtc_pre_config(prtc);
	/* check if RTC has aready been configured */
	if (BKP_VALUE != RTC_BKP0){
		rtc_setup(&prtc->rtcSetting);
	}else{
		/* detect the reset source */
		if (RESET != rcu_flag_get(RCU_FLAG_PORRST)){
		}
		else if (RESET != rcu_flag_get(RCU_FLAG_EPRST)){
		}
//		rtc_show_time(prtc);
	}
	rcu_all_reset_flag_clear();
	rtc_timestamp_enable(RTC_TIMESTAMP_FALLING_EDGE);
	rtc_interrupt_enable(RTC_INT_TIMESTAMP);
	rtc_flag_clear(RTC_FLAG_TS|RTC_FLAG_TSOVR);
}

/*!
    \brief      RTC configuration function
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_pre_config(RTCTypeDef* prtc)
{
#if defined (RTC_CLOCK_SOURCE_IRC32K)
	rcu_osci_on(RCU_IRC32K);
	rcu_osci_stab_wait(RCU_IRC32K);
	rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);

	// IRC32K frequency is approximately 32kHz, but can vary from 30-33kHz
	// Using prescaler values for 32kHz: prescaler_a * prescaler_s = 32000
	// prescaler_a = 99, prescaler_s = 319 (99+1) * (319+1) = 32000
	// This gives us 1Hz RTC clock
	//prescaler_s = 0x13F;  // 319
	//prescaler_a = 0x63;   // 99
#elif defined (RTC_CLOCK_SOURCE_LXTAL)
	rcu_osci_on(RCU_LXTAL);
	rcu_osci_stab_wait(RCU_LXTAL);
	rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);

#else
#error RTC clock source should be defined.
#endif /* RTC_CLOCK_SOURCE_IRC32K */

	rcu_periph_clock_enable(prtc->rtcClk);
	rtc_register_sync_wait();
}

/*!
    \brief      use hyperterminal to setup RTC time and alarm
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_setup(rtc_parameter_struct* pRTCPara)
{
#if defined (RTC_CLOCK_SOURCE_IRC32K)
	// For IRC32K (32kHz internal RC oscillator)
	// Total division = (factor_asyn + 1) * (factor_syn + 1) = 32000
	// To get 1Hz RTC clock from 32kHz IRC32K
	// Using factor_asyn = 99, factor_syn = 319
	// (99+1) * (319+1) = 100 * 320 = 32000
	pRTCPara->factor_asyn = 99;   // Asynchronous prescaler
	pRTCPara->factor_syn = 319;   // Synchronous prescaler
#elif defined (RTC_CLOCK_SOURCE_LXTAL)
	// For LXTAL (32.768kHz external crystal)
	// Total division = (factor_asyn + 1) * (factor_syn + 1) = 32768
	// Using factor_asyn = 127, factor_syn = 255
	// (127+1) * (255+1) = 128 * 256 = 32768
	pRTCPara->factor_asyn = 127;
	pRTCPara->factor_syn = 255;
#endif
	/* RTC current time configuration */
	rtc_init(pRTCPara);
}

/*!
    \brief      display the timestamp time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_show_timestamp(RTCTypeDef* prtc,TimeStampTypeDef* pTime)
{
	uint32_t ts_subsecond = 0;
	uint8_t ts_subsecond_ss,ts_subsecond_ts,ts_subsecond_hs ;
	memset(pTime,0,sizeof(TimeStampTypeDef));
	rtc_timestamp_get(&prtc->timestamp);
	/* get the subsecond value of timestamp time, and convert it into fractional format */
	ts_subsecond = rtc_timestamp_subsecond_get();
#if defined (RTC_CLOCK_SOURCE_IRC32K)
	// For IRC32K: synchronous prescaler = 319, so subsecond range is 0-319
	// Convert to milliseconds: (319 - subsecond) * 1000 / 320
	ts_subsecond_ss=(1000-(ts_subsecond*1000+1000)/320)/100;
	ts_subsecond_ts=(1000-(ts_subsecond*1000+1000)/320)%100/10;
	ts_subsecond_hs=(1000-(ts_subsecond*1000+1000)/320)%10;
#elif defined (RTC_CLOCK_SOURCE_LXTAL)
	// For LXTAL: synchronous prescaler = 255, so subsecond range is 0-255
	// Original calculation for 32.768kHz crystal
	ts_subsecond_ss=(1000-(ts_subsecond*1000+1000)/256)/100;
	ts_subsecond_ts=(1000-(ts_subsecond*1000+1000)/256)%100/10;
	ts_subsecond_hs=(1000-(ts_subsecond*1000+1000)/256)%10;
#endif
	
	sprintf(pTime->year,"20%d",prtc->rtcSetting.year);
	switch(prtc->timestamp.timestamp_month)
	{
		case RTC_JAN:	memcpy(pTime->month,"Jan",4);break;
		case RTC_FEB:	memcpy(pTime->month,"Feb",4);break;
		case RTC_MAR:	memcpy(pTime->month,"Mar",4);break;
		case RTC_APR:	memcpy(pTime->month,"Apr",4);break;
		case RTC_MAY:	memcpy(pTime->month,"May",4);break;
		case RTC_JUN:	memcpy(pTime->month,"Jun",4);break;
		case RTC_JUL:	memcpy(pTime->month,"Jul",4);break;
		case RTC_AUG:	memcpy(pTime->month,"Aug",4);break;
		case RTC_SEP:	memcpy(pTime->month,"Sep",4);break;
		case RTC_OCT:	memcpy(pTime->month,"Oct",4);break;
		case RTC_NOV:	memcpy(pTime->month,"Nov",4);break;
		case RTC_DEC:	memcpy(pTime->month,"Dec",4);break;
		default:break;
	}
	sprintf(pTime->day,"%d",prtc->timestamp.timestamp_day);
	sprintf(pTime->hour,"%0.2x",prtc->timestamp.timestamp_hour);
	sprintf(pTime->minute,"%0.2x",prtc->timestamp.timestamp_minute);
	sprintf(pTime->second,"%0.2x",prtc->timestamp.timestamp_second);
	sprintf(pTime->miniSecond,"%d%d%d",ts_subsecond_ss, ts_subsecond_ts, ts_subsecond_hs);
}

/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/
//void rtc_show_time(RTCTypeDef* prtc)
//{
//	uint32_t time_subsecond = 0;
//	uint8_t subsecond_ss = 0,subsecond_ts = 0,subsecond_hs = 0;

//	rtc_current_time_get(&prtc->rtcSetting);
//	/* get the subsecond value of current time, and convert it into fractional format */
//	time_subsecond = rtc_subsecond_get();
//#if defined (RTC_CLOCK_SOURCE_IRC32K)
//	// For IRC32K: synchronous prescaler = 319, so subsecond range is 0-319
//	subsecond_ss=(1000-(time_subsecond*1000+1000)/320)/100;
//	subsecond_ts=(1000-(time_subsecond*1000+1000)/320)%100/10;
//	subsecond_hs=(1000-(time_subsecond*1000+1000)/320)%10;
//#elif defined (RTC_CLOCK_SOURCE_LXTAL)
//	// For LXTAL: synchronous prescaler = 255, so subsecond range is 0-255
//	subsecond_ss=(1000-(time_subsecond*1000+1000)/256)/100;
//	subsecond_ts=(1000-(time_subsecond*1000+1000)/256)%100/10;
//	subsecond_hs=(1000-(time_subsecond*1000+1000)/256)%10;
//#endif
//}

void timeSync(int week, double sec)
{
	rtc_parameter_struct rtc_arg;
	gtime_t curTime;
	struct tm time;
	curTime = gpst2time(week,sec);			//GPS������ת����ʱ��ṹ��
	curTime = gpst2utc(curTime);			//GPSʱ��ת����UTCʱ��
	
	memcpy(&time,localtime(&curTime.time),sizeof(struct tm));
	rtc_arg.day_of_week = time.tm_wday;
	rtc_arg.display_format = RTC_24HOUR;
	rtc_arg.year = time.tm_year/10 + time.tm_year%10;
	rtc_arg.month = time.tm_mon/10 + time.tm_mon%10;
	rtc_arg.date = time.tm_mday/10 + time.tm_mday%10;
	rtc_arg.hour = time.tm_hour/10 + time.tm_hour%10;
	rtc_arg.minute = time.tm_min/10 + time.tm_min%10;
	rtc_arg.second = time.tm_sec/10 + time.tm_sec%10;
	
	if(rtc_arg.hour > 12)
		rtc_arg.am_pm = RTC_PM;
	else
		rtc_arg.am_pm = RTC_AM;
	rtc_setup(&rtc_arg);
}

int isLeapYear(int year)
{
	if((year%400 == 0)||(year%4==0&&year%100 != 0))
		return 1;
	else
		return 0;
}

int yearDay(int year, int mon, int day)
{
	int yearday = 0;
	switch(mon)
	{
		case 1:yearday = 0;break;
		case 2:yearday = 31;break;
		case 3:yearday = 59;break;
		case 4:yearday = 90;break;
		case 5:yearday = 120;break;
		case 6:yearday = 151;break;
		case 7:yearday = 181;break;
		case 8:yearday = 212;break;
		case 9:yearday = 243;break;
		case 10:yearday = 273;break;
		case 11:yearday = 304;break;
		case 12:yearday = 334;break;
	}
	if(isLeapYear(year))
		yearday = yearday + 1;
	return yearday + day;
}

void getRTCWeekSecond(int* pWeek, double* pSec)
{
	gtime_t curTime;
	struct tm tTM;
	uint32_t time_subsecond = 0;
	uint8_t subsecond_ss = 0,subsecond_ts = 0,subsecond_hs = 0;
	rtc_parameter_struct rtc_arg;
	rtc_current_time_get(&rtc_arg);
	
	tTM.tm_year = (rtc_arg.year>>4)*10+(rtc_arg.year&0x0F);
	tTM.tm_mon = (rtc_arg.month>>4)*10+(rtc_arg.month&0x0F);
	tTM.tm_mday = (rtc_arg.date>>4)*10+(rtc_arg.date&0x0F);
	tTM.tm_hour = (rtc_arg.hour>>4)*10+(rtc_arg.hour&0x0F);;
	tTM.tm_min = (rtc_arg.minute>>4)*10+(rtc_arg.minute&0x0F);;
	tTM.tm_sec = (rtc_arg.second>>4)*10+(rtc_arg.second&0x0F);;
	tTM.tm_wday = rtc_arg.day_of_week;
	tTM.tm_yday = yearDay(tTM.tm_year,tTM.tm_mon,tTM.tm_mday);
	tTM.tm_isdst = -1;
	curTime.time = mktime(&tTM);
	time_subsecond = rtc_subsecond_get();
#if defined (RTC_CLOCK_SOURCE_IRC32K)
	// For IRC32K: synchronous prescaler = 319, so subsecond range is 0-319
	// Convert to milliseconds: (319 - subsecond) * 1000 / 320
	subsecond_ss=(1000-(time_subsecond*1000+1000)/320)/100;
	subsecond_ts=(1000-(time_subsecond*1000+1000)/320)%100/10;
	subsecond_hs=(1000-(time_subsecond*1000+1000)/320)%10;
#elif defined (RTC_CLOCK_SOURCE_LXTAL)
	// For LXTAL: synchronous prescaler = 255, so subsecond range is 0-255
	// Original calculation for 32.768kHz crystal
	subsecond_ss=(1000-(time_subsecond*1000+1000)/256)/100;
	subsecond_ts=(1000-(time_subsecond*1000+1000)/256)%100/10;
	subsecond_hs=(1000-(time_subsecond*1000+1000)/256)%10;
#endif
	curTime.sec = subsecond_ss*0.1f + subsecond_ts* 0.01f + subsecond_hs*0.001f;
	*pSec= time2gpst(curTime,pWeek);
}
/*!
    \brief      this function handles RTC interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
void TAMPER_STAMP_IRQHandler(void)
{
	if(RESET != rtc_flag_get(RTC_FLAG_TS)){
		rtc_show_timestamp(pRTC,pTimestamep);
		rtc_flag_clear(RTC_FLAG_TS|RTC_FLAG_TSOVR); 
	} 
}

