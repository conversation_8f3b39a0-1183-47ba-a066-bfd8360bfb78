#include "INS912ALGORITHMENTRY.h"
#include "deviceconfig.h"
#include "appmain.h"
//#include "ins.h"
//#include "DATASTRUCT.h"
//#include "FUNCTION.h"
//#include "GLOBALDATA.h"
//#include "insTestingEntry.h"
//#include "datado.h"


unsigned int gprotocol_send_baudrate = BAUD_RATE_2000000;//BAUD_RATE_2000000;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600, BAUD_RATE_2000000

unsigned int NaviCompute_do_count =0;

void Algorithm_before_otherDataDo(navcanin_t		canin)
	//设备将can数据
{
	caninfupdate(&canin);
}

void SendVersionInfo(void)
	//串口发送版本信息
{
		char navversion[32];
		//---update 2024.5.31--------------
#ifndef CMPL_CODE_EDWOY
strcpy(navversion, "INS912-3A project v1.0.0\r\n");  
#else //<Edwoy codes>  
	strcpy(navversion, "INS370-21J project v1.0.0\r\n");    
  //INS_Init();
#endif   

	uart4sendmsg(navversion, strlen(navversion));
	uart4sendmsg(navversion, strlen(navversion));
 	
}

void SysInit_Over(void)
	//系统初始化完成后处理的事务
{

	INS_Init();
	
	SendVersionInfo();
	
	fpga_syn_count=0;
	fpga_loop_count=0;
}

void loopDoOther(void)
	//在主循环中，处理非FPAG及算法的其它事务
{
		if (gbilldebuguart4)  {  uart4sendmsg_canout(&gCanRxBuf);}		//
		
		LEDIndicator(g_LEDIndicatorState);	
	
}
