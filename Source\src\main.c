/*!
    \file  	main.c
    \brief 	ins912-3a project
	\author	Bill
	\data	2023/10/27
*/
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"
#include "FirmwareUpdateFile.h"
#include "computerFrameParse.h"
#include "gd32f4xx_rcu.h"


void ReturnColliery_Operate(void)
{   

    uint8_t crc=0;
    parabag_CollieryOperate_back stSendPara;//定义回应结构体变量
    uint16_t BaoLen = sizeof(parabag_CollieryOperate_back);
    uint8_t SendDatabuf[sizeof(parabag_CollieryOperate_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_CollieryOperate_back));

	stSendPara.info.ReturnFlag= 0x5a;
    //配置帧头
    SendPara_SetHead((p_parabag_Other_back)&stSendPara,SETPARA_TYPE1_COLLIERY_OPERATE,BaoLen);
    //配置帧尾
    SendPara_SetEnd((p_parabag_Other_back)&stSendPara,BaoLen);

    memcpy(SendDatabuf,&stSendPara,BaoLen);
    uart4sendmsg((char*)SendDatabuf,BaoLen);
}

 //MCU系统重启
 void Drv_SystemReset(void)
 {
	 NVIC_SystemReset();
 }

 extern uint8_t CollieryOperate_Flag ; //自寻北标志，由上位机发送
 uint8_t CollieryOperate_uartsend_Flag = 0; //自寻北UART发送标志，
 uint8_t CollieryOperate_goto_Flag = 1; //自寻北goto标志，
 uint8_t Return_Colliery_Operate_Flag = 1;


int main(void)
{

jump:
  //delay_ms(1000);
    
	SysInit();				// Init inertial navigation device...
	
	SysInit_Over();		//系统初始化完成后，进入循环前的准备
	ReadParaFromFlash();
	Return_Colliery_Operate_Flag = 1;
	while(1) 
	{		
		if(fpga_syn) 
		{	//每一帧FPGA数据产生，处理
			fpga_syn = 0;
			
			get_fpgadata();				//1、获取当前帧FPGA数据，及相关
			AlgorithmDo();			//2、对获取的数据进行算法处理	
			if((g_InertialSysAlign.AlignTime >= TIME_COARSE_ALIGN)&&(Return_Colliery_Operate_Flag == 1))
            {
				ReturnColliery_Operate();
				CollieryOperate_uartsend_Flag = 1;
				CollieryOperate_goto_Flag = 0;
				Return_Colliery_Operate_Flag = 0;
			}
            if(CollieryOperate_Flag == 1)
            {   
                 CollieryOperate_uartsend_Flag = 0;
                 CollieryOperate_goto_Flag = 1;
                 CollieryOperate_Flag = 0;
                 goto jump;
				 
			}
			INS912_Output(&gnavout);    //4、算法处理完成的数据，进行打包、发送处理			
		} 
		
		loopDoOther();								//循环中，处理其它事宜

		analysisRxdata();							//
    

	}
}



