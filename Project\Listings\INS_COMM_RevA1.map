Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]
1
==============================================================================

Section Cross References

    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for .constdata
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for .bss
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_it.o(i.ADC_IRQHandler) refers to gd32f4xx_adc.o(i.adc_interrupt_flag_clear) for adc_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI10_15_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_get) for exti_interrupt_flag_get
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr4
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to bsp_fmc.o(i.DRam_Read) for DRam_Read
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to f2d.o(.text) for __aeabi_f2d
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.bss) for hINSFPGAData
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.data) for fpga_data_read_flag
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.data) for g_second
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.data) for g_week
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.data) for fpga_setting_update_flag
    gd32f4xx_it.o(i.EXTI5_9_IRQHandler) refers to ins_data.o(.bss) for hFPGASetting
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_tim.o(.data) for time_periodic_sec_cnt
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_tim.o(.data) for time_periodic_min_cnt
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_tim.o(.data) for time_periodic_hour_cnt
    gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) refers to bsp_tim.o(.data) for time_sync_flag
    gd32f4xx_it.o(i.TIMER1_IRQHandler) refers to bsp_tim.o(.data) for time_base_periodic_cnt
    main.o(i.GetChipID) refers to ins_data.o(.bss) for hSetting
    main.o(i.GetChipID) refers to ins_data.o(.bss) for hDefaultSetting
    main.o(i.INS_Init) refers to systick.o(i.delay_init) for delay_init
    main.o(i.INS_Init) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    main.o(i.INS_Init) refers to main.o(i.SetDefaultProductInfo) for SetDefaultProductInfo
    main.o(i.INS_Init) refers to bsp_gpio.o(i.bsp_gpio_init) for bsp_gpio_init
    main.o(i.INS_Init) refers to bsp_rtc.o(i.bsp_rtc_init) for bsp_rtc_init
    main.o(i.INS_Init) refers to main.o(i.trng_configuration) for trng_configuration
    main.o(i.INS_Init) refers to bsp_fmc.o(i.exmc_asynchronous_sram_init) for exmc_asynchronous_sram_init
    main.o(i.INS_Init) refers to tcpserver.o(i.TCPServer_Init) for TCPServer_Init
    main.o(i.INS_Init) refers to bsp_tim.o(i.bsp_tim_init) for bsp_tim_init
    main.o(i.INS_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.INS_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    main.o(i.INS_Init) refers to systick.o(i.delay_ms) for delay_ms
    main.o(i.INS_Init) refers to ch378_spi_hw.o(i.mInitCH378Host) for mInitCH378Host
    main.o(i.INS_Init) refers to bsp_rtc.o(.data) for pRTC
    main.o(i.LEDIndicator) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.LEDIndicator) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    main.o(i.SetDefaultProductInfo) refers to bsp_flash.o(i.ReadFlash) for ReadFlash
    main.o(i.SetDefaultProductInfo) refers to main.o(i.GetChipID) for GetChipID
    main.o(i.SetDefaultProductInfo) refers to bsp_flash.o(i.WriteFlash) for WriteFlash
    main.o(i.SetDefaultProductInfo) refers to ins_data.o(.bss) for hSetting
    main.o(i.SetDefaultProductInfo) refers to ins_data.o(.bss) for hDefaultSetting
    main.o(i.StartNavigation) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.StartNavigation) refers to systick.o(i.delay_us) for delay_us
    main.o(i.StartNavigation) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    main.o(i.checkUSBReady) refers to file_sys.o(i.CH378DiskConnect) for CH378DiskConnect
    main.o(i.checkUSBReady) refers to file_sys.o(i.CH378DiskReady) for CH378DiskReady
    main.o(i.checkUSBReady) refers to file_sys.o(i.CH378GetDiskStatus) for CH378GetDiskStatus
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.loggingLogFile) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.loggingLogFile) refers to logger.o(i.generateCSVLogFileName) for generateCSVLogFileName
    main.o(i.loggingLogFile) refers to logger.o(i.writeCSVLog) for writeCSVLog
    main.o(i.main) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    main.o(i.main) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    main.o(i.main) refers to main.o(i.INS_Init) for INS_Init
    main.o(i.main) refers to bsp_rtc.o(i.timeSync) for timeSync
    main.o(i.main) refers to logger.o(i.generateCSVLogFileName) for generateCSVLogFileName
    main.o(i.main) refers to logger.o(i.writeCSVLog) for writeCSVLog
    main.o(i.main) refers to main.o(i.LEDIndicator) for LEDIndicator
    main.o(i.main) refers to bsp_rtc.o(i.getRTCWeekSecond) for getRTCWeekSecond
    main.o(i.main) refers to bsp_fmc.o(i.DRam_Write) for DRam_Write
    main.o(i.main) refers to ins_data.o(.data) for g_LEDIndicatorState
    main.o(i.main) refers to ins_data.o(.data) for fpga_data_read_flag
    main.o(i.main) refers to bsp_tim.o(.data) for time_sync_flag
    main.o(i.main) refers to ins_data.o(.data) for g_second
    main.o(i.main) refers to ins_data.o(.data) for g_week
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.trng_configuration) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    main.o(i.trng_configuration) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    main.o(i.trng_configuration) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    main.o(i.trng_configuration) refers to main.o(i.trng_ready_check) for trng_ready_check
    main.o(i.trng_ready_check) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    main.o(.data) refers to main.o(.data) for g_LogBuf
    systick.o(i.delay_1ms) refers to systick.o(.data) for .data
    systick.o(i.delay_decrement) refers to systick.o(.data) for .data
    systick.o(i.delay_init) refers to gd32f4xx_misc.o(i.systick_clksource_set) for systick_clksource_set
    systick.o(i.delay_init) refers to systick.o(.data) for .data
    systick.o(i.delay_ms) refers to systick.o(.data) for .data
    systick.o(i.delay_us) refers to systick.o(.data) for .data
    systick.o(i.delay_xms) refers to systick.o(i.delay_us) for delay_us
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_RMC_Buff_Parser) for gnss_RMC_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_GGA_Buff_Parser) for gnss_GGA_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_VTG_Buff_Parser) for gnss_VTG_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_ZDA_Buff_Parser) for gnss_ZDA_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_Heading_Buff_Parser) for gnss_Heading_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_BESTPOS_Buff_Parser) for gnss_BESTPOS_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(i.gnss_TRA_Buff_Parser) for gnss_TRA_Buff_Parser
    gnss.o(i.GNSS_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.GNSS_Cmd_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.GNSS_Cmd_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_BESTPOS_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_Fetch_Data) refers to d2f.o(.text) for __aeabi_d2f
    gnss.o(i.gnss_Fetch_Data) refers to gnss.o(.bss) for .bss
    gnss.o(i.gnss_GGA_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_GGA_Buff_Parser) refers to data_convert.o(i.dtoc) for dtoc
    gnss.o(i.gnss_GGA_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_GGA_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_GGA_Buff_Parser) refers to gnss.o(.bss) for .bss
    gnss.o(i.gnss_Heading_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_Heading_Buff_Parser) refers to data_convert.o(i.dtoc) for dtoc
    gnss.o(i.gnss_Heading_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_Heading_Buff_Parser) refers to gnss.o(.bss) for .bss
    gnss.o(i.gnss_RMC_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_RMC_Buff_Parser) refers to data_convert.o(i.dtoc) for dtoc
    gnss.o(i.gnss_RMC_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_RMC_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_RMC_Buff_Parser) refers to gnss.o(.bss) for .bss
    gnss.o(i.gnss_TRA_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_TRA_Buff_Parser) refers to data_convert.o(i.dtoc) for dtoc
    gnss.o(i.gnss_TRA_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_TRA_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_TRA_Buff_Parser) refers to gnss.o(.bss) for .bss
    gnss.o(i.gnss_VTG_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_VTG_Buff_Parser) refers to data_convert.o(i.dtoc) for dtoc
    gnss.o(i.gnss_VTG_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_VTG_Buff_Parser) refers to gnss.o(.bss) for .bss
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to strstr.o(.text) for strstr
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to data_convert.o(i.dtoc) for dtoc
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to atoi.o(.text) for atoi
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to dflti.o(.text) for __aeabi_i2d
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to gnss.o(.data) for .data
    gnss.o(i.gnss_ZDA_Buff_Parser) refers to gnss.o(.bss) for .bss
    ins_data.o(i.read_flash) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    ins_data.o(i.read_flash) refers to bsp_flash.o(i.ReadFlash) for ReadFlash
    ins_data.o(i.read_flash) refers to ins_data.o(.bss) for .bss
    ins_data.o(i.save_flash) refers to bsp_flash.o(i.InitFlashAddr) for InitFlashAddr
    ins_data.o(i.save_flash) refers to bsp_flash.o(i.WriteFlash) for WriteFlash
    ins_data.o(i.save_flash) refers to bsp_flash.o(i.EndWrite) for EndWrite
    ins_data.o(i.save_flash) refers to ins_data.o(.bss) for .bss
    tcpserver.o(i.TCPServer_Init) refers to ch395spi.o(i.CH395_PORT_INIT) for CH395_PORT_INIT
    tcpserver.o(i.TCPServer_Init) refers to ch395spi.o(i.CH395_RST) for CH395_RST
    tcpserver.o(i.TCPServer_Init) refers to systick.o(i.delay_ms) for delay_ms
    tcpserver.o(i.TCPServer_Init) refers to ch395cmd.o(i.CH395SetStartPara) for CH395SetStartPara
    tcpserver.o(i.TCPServer_Init) refers to tcpserver.o(i.socket_buffer_config) for socket_buffer_config
    tcpserver.o(i.TCPServer_Init) refers to ch395cmd.o(i.CH395CMDInitCH395) for CH395CMDInitCH395
    tcpserver.o(i.TCPServer_Init) refers to tcpserver.o(i.ch395_socket_tcp_server_init) for ch395_socket_tcp_server_init
    tcpserver.o(i.TCPServer_Init) refers to ch395cmd.o(i.CH395TCPListen) for CH395TCPListen
    tcpserver.o(i.TCPServer_Init) refers to tcpserver.o(.data) for .data
    tcpserver.o(i.TCPServer_Process) refers to ch395cmd.o(i.CH395CMDGetPHYStatus) for CH395CMDGetPHYStatus
    tcpserver.o(i.TCPServer_Process) refers to ch395cmd.o(i.CH395DHCPEnable) for CH395DHCPEnable
    tcpserver.o(i.TCPServer_Process) refers to ch395cmd.o(i.CH395GetDHCPStatus) for CH395GetDHCPStatus
    tcpserver.o(i.TCPServer_Process) refers to ch395cmd.o(i.CH395GetIPInf) for CH395GetIPInf
    tcpserver.o(i.TCPServer_Process) refers to ch395cmd.o(i.CH395CMDGetUnreachIPPT) for CH395CMDGetUnreachIPPT
    tcpserver.o(i.TCPServer_Process) refers to tcpserver.o(i.ch395_socket_tcp_client_interrupt) for ch395_socket_tcp_client_interrupt
    tcpserver.o(i.TCPServer_Process) refers to tcpserver.o(.data) for .data
    tcpserver.o(i.TCPServer_Process) refers to tcpserver.o(.bss) for .bss
    tcpserver.o(i.ch395_socket_tcp_client_interrupt) refers to ch395cmd.o(i.CH395GetSocketInt) for CH395GetSocketInt
    tcpserver.o(i.ch395_socket_tcp_client_interrupt) refers to ch395cmd.o(i.CH395GetRecvLength) for CH395GetRecvLength
    tcpserver.o(i.ch395_socket_tcp_client_interrupt) refers to ch395cmd.o(i.CH395GetRecvData) for CH395GetRecvData
    tcpserver.o(i.ch395_socket_tcp_client_interrupt) refers to ch395cmd.o(i.CH395SendData) for CH395SendData
    tcpserver.o(i.ch395_socket_tcp_client_interrupt) refers to ch395cmd.o(i.CH395CMDGetRemoteIPP) for CH395CMDGetRemoteIPP
    tcpserver.o(i.ch395_socket_tcp_client_interrupt) refers to tcpserver.o(.bss) for .bss
    tcpserver.o(i.ch395_socket_tcp_server_init) refers to ch395cmd.o(i.CH395SetSocketProtType) for CH395SetSocketProtType
    tcpserver.o(i.ch395_socket_tcp_server_init) refers to ch395cmd.o(i.CH395SetSocketSourPort) for CH395SetSocketSourPort
    tcpserver.o(i.ch395_socket_tcp_server_init) refers to ch395cmd.o(i.CH395OpenSocket) for CH395OpenSocket
    tcpserver.o(i.ch395_socket_tcp_server_init) refers to tcpserver.o(.data) for .data
    tcpserver.o(i.socket_buffer_config) refers to ch395cmd.o(i.CH395SetSocketRecvBuf) for CH395SetSocketRecvBuf
    tcpserver.o(i.socket_buffer_config) refers to ch395cmd.o(i.CH395SetSocketSendBuf) for CH395SetSocketSendBuf
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.gpst2time) for gpst2time
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.bdt2gpst) for bdt2gpst
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.gpst2utc) for gpst2utc
    time_unify.o(i.Bdt2UtcTime) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.bdt2gpst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.bdt2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.bdt2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.bdt2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.bdt2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.bdt2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.bdt2time) refers to time_unify.o(.data) for .data
    time_unify.o(i.epoch2time) refers to memcpya.o(.text) for __aeabi_memcpy4
    time_unify.o(i.epoch2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.epoch2time) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    time_unify.o(i.epoch2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.epoch2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.epoch2time) refers to time_unify.o(.constdata) for .constdata
    time_unify.o(i.gpst2bdt) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.gpst2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gpst2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.gpst2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.gpst2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.gpst2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.gpst2time) refers to time_unify.o(.data) for .data
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gpst2utc) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.gpst2utc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    time_unify.o(i.gpst2utc) refers to time_unify.o(.data) for .data
    time_unify.o(i.gst2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.gst2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.gst2time) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.gst2time) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.gst2time) refers to dadd.o(.text) for __aeabi_drsub
    time_unify.o(i.gst2time) refers to time_unify.o(.data) for .data
    time_unify.o(i.str2time) refers to scanf_fp.o(.text) for _scanf_real
    time_unify.o(i.str2time) refers to strlen.o(.text) for strlen
    time_unify.o(i.str2time) refers to __0sscanf.o(.text) for __0sscanf
    time_unify.o(i.str2time) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.str2time) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.str2time) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2bdt) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2bdt) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2bdt) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2bdt) refers to time_unify.o(.data) for .data
    time_unify.o(i.time2epoch) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.time2epoch) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2epoch) refers to time_unify.o(.data) for .data
    time_unify.o(i.time2gpst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2gpst) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2gpst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2gpst) refers to time_unify.o(.data) for .data
    time_unify.o(i.time2gst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2gst) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.time2gst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2gst) refers to time_unify.o(.data) for .data
    time_unify.o(i.time2sec) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.time2sec) refers to dmul.o(.text) for __aeabi_dmul
    time_unify.o(i.time2sec) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.time2sec) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.time2str) refers to dflti.o(.text) for __aeabi_i2d
    time_unify.o(i.time2str) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    time_unify.o(i.time2str) refers to ddiv.o(.text) for __aeabi_ddiv
    time_unify.o(i.time2str) refers to dadd.o(.text) for __aeabi_dsub
    time_unify.o(i.time2str) refers to cdcmple.o(.text) for __aeabi_cdcmple
    time_unify.o(i.time2str) refers to time_unify.o(i.time2epoch) for time2epoch
    time_unify.o(i.time2str) refers to printfa.o(i.__0sprintf) for __2sprintf
    time_unify.o(i.timeadd) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.timeadd) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    time_unify.o(i.timeadd) refers to dfixi.o(.text) for __aeabi_d2iz
    time_unify.o(i.timediff) refers to dadd.o(.text) for __aeabi_dsub
    time_unify.o(i.timediff) refers to dfltui.o(.text) for __aeabi_ui2d
    time_unify.o(i.utc2gmst) refers to memcpya.o(.text) for __aeabi_memcpy4
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.time2sec) for time2sec
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.utc2gmst) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.utc2gmst) refers to ddiv.o(.text) for __aeabi_ddiv
    time_unify.o(i.utc2gmst) refers to dmul.o(.text) for __aeabi_dmul
    time_unify.o(i.utc2gmst) refers to dadd.o(.text) for __aeabi_dadd
    time_unify.o(i.utc2gmst) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    time_unify.o(i.utc2gmst) refers to time_unify.o(.constdata) for .constdata
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.epoch2time) for epoch2time
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.timediff) for timediff
    time_unify.o(i.utc2gpst) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    time_unify.o(i.utc2gpst) refers to time_unify.o(i.timeadd) for timeadd
    time_unify.o(i.utc2gpst) refers to time_unify.o(.data) for .data
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.compensate_temperature) for compensate_temperature
    bmp2.o(i.bmp2_compensate_data) refers to bmp2.o(i.compensate_pressure) for compensate_pressure
    bmp2.o(i.bmp2_compute_meas_time) refers to memcpya.o(.text) for __aeabi_memcpy4
    bmp2.o(i.bmp2_compute_meas_time) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_compute_meas_time) refers to bmp2.o(.constdata) for .constdata
    bmp2.o(i.bmp2_get_config) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_power_mode) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_regs) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_get_sensor_data) refers to bmp2.o(i.bmp2_compensate_data) for bmp2_compensate_data
    bmp2.o(i.bmp2_get_status) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.bmp2_init) refers to bmp2.o(i.get_calib_param) for get_calib_param
    bmp2.o(i.bmp2_set_config) refers to bmp2.o(i.conf_sensor) for conf_sensor
    bmp2.o(i.bmp2_set_power_mode) refers to bmp2.o(i.conf_sensor) for conf_sensor
    bmp2.o(i.bmp2_set_regs) refers to bmp2.o(i.null_ptr_check) for null_ptr_check
    bmp2.o(i.bmp2_soft_reset) refers to bmp2.o(i.bmp2_set_regs) for bmp2_set_regs
    bmp2.o(i.compensate_pressure) refers to dflti.o(.text) for __aeabi_i2d
    bmp2.o(i.compensate_pressure) refers to dmul.o(.text) for __aeabi_dmul
    bmp2.o(i.compensate_pressure) refers to dadd.o(.text) for __aeabi_dsub
    bmp2.o(i.compensate_pressure) refers to dfltui.o(.text) for __aeabi_ui2d
    bmp2.o(i.compensate_pressure) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bmp2.o(i.compensate_pressure) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    bmp2.o(i.compensate_pressure) refers to ddiv.o(.text) for __aeabi_ddiv
    bmp2.o(i.compensate_temperature) refers to dflti.o(.text) for __aeabi_i2d
    bmp2.o(i.compensate_temperature) refers to dfltui.o(.text) for __aeabi_ui2d
    bmp2.o(i.compensate_temperature) refers to dmul.o(.text) for __aeabi_dmul
    bmp2.o(i.compensate_temperature) refers to dadd.o(.text) for __aeabi_dsub
    bmp2.o(i.compensate_temperature) refers to dfixi.o(.text) for __aeabi_d2iz
    bmp2.o(i.compensate_temperature) refers to ddiv.o(.text) for __aeabi_ddiv
    bmp2.o(i.compensate_temperature) refers to cdcmple.o(.text) for __aeabi_cdcmple
    bmp2.o(i.compensate_temperature) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_soft_reset) for bmp2_soft_reset
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.set_os_mode) for set_os_mode
    bmp2.o(i.conf_sensor) refers to bmp2.o(i.bmp2_set_regs) for bmp2_set_regs
    bmp2.o(i.get_calib_param) refers to memseta.o(.text) for __aeabi_memclr4
    bmp2.o(i.get_calib_param) refers to bmp2.o(i.bmp2_get_regs) for bmp2_get_regs
    bmp280.o(i.bmp280_get_data) refers to bmp2.o(i.bmp2_get_status) for bmp2_get_status
    bmp280.o(i.bmp280_get_data) refers to bmp2.o(i.bmp2_get_sensor_data) for bmp2_get_sensor_data
    bmp280.o(i.bmp280_init) refers to common.o(i.bmp2_interface_selection) for bmp2_interface_selection
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_init) for bmp2_init
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_get_config) for bmp2_get_config
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_set_config) for bmp2_set_config
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_set_power_mode) for bmp2_set_power_mode
    bmp280.o(i.bmp280_init) refers to bmp2.o(i.bmp2_compute_meas_time) for bmp2_compute_meas_time
    bmp280.o(i.bmp280_init) refers to bmp280.o(.bss) for .bss
    bmp280.o(i.bmp280_init) refers to bmp280.o(.data) for .data
    bsp_adc.o(i.adc_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_regular_channel_config) for adc_regular_channel_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_threshold_config) for adc_watchdog_threshold_config
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable) for adc_watchdog_single_channel_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable) for adc_watchdog_group_channel_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_interrupt_enable) for adc_interrupt_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    bsp_adc.o(i.adc_config) refers to systick.o(i.delay_ms) for delay_ms
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    bsp_adc.o(i.adc_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    bsp_adc.o(i.adc_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_adc.o(i.enter_sleep_mode) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_adc.o(i.enter_sleep_mode) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_enable) for rcu_bkp_reset_enable
    bsp_can.o(i.CAN0_RX1_IRQHandler) refers to gd32f4xx_can.o(i.can_interrupt_flag_get) for can_interrupt_flag_get
    bsp_can.o(i.CAN0_RX1_IRQHandler) refers to gd32f4xx_can.o(i.can_message_receive) for can_message_receive
    bsp_can.o(i.CAN0_RX1_IRQHandler) refers to bsp_can.o(.data) for .data
    bsp_can.o(i.CAN0_RX1_IRQHandler) refers to ins_data.o(.bss) for hINSData
    bsp_can.o(i.CAN1_RX1_IRQHandler) refers to gd32f4xx_can.o(i.can_message_receive) for can_message_receive
    bsp_can.o(i.CAN1_RX1_IRQHandler) refers to bsp_can.o(.data) for .data
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_deinit) for can_deinit
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_struct_para_init) for can_struct_para_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_init) for can_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_filter_init) for can_filter_init
    bsp_can.o(i.bsp_can_init) refers to gd32f4xx_can.o(i.can_interrupt_enable) for can_interrupt_enable
    bsp_can.o(i.bsp_can_transmit) refers to gd32f4xx_can.o(i.can_struct_para_init) for can_struct_para_init
    bsp_can.o(i.bsp_can_transmit) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_can.o(i.bsp_can_transmit) refers to gd32f4xx_can.o(i.can_message_transmit) for can_message_transmit
    bsp_can.o(i.can_transmit_acc) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_acc) refers to ins_data.o(.bss) for hINSData
    bsp_can.o(i.can_transmit_angle) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_angle) refers to ins_data.o(.bss) for hINSData
    bsp_can.o(i.can_transmit_gyro) refers to bsp_can.o(i.bsp_can_transmit) for bsp_can_transmit
    bsp_can.o(i.can_transmit_gyro) refers to ins_data.o(.bss) for hINSData
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    bsp_exti.o(i.bsp_exti_config) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    bsp_exti.o(i.bsp_exti_init) refers to bsp_exti.o(i.bsp_exti_config) for bsp_exti_config
    bsp_exti.o(i.bsp_exti_init) refers to bsp_exti.o(.data) for .data
    bsp_flash.o(i.EndWrite) refers to bsp_flash.o(i.WriteOld) for WriteOld
    bsp_flash.o(i.EndWrite) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_flash.o(i.EndWrite) refers to bsp_flash.o(.data) for .data
    bsp_flash.o(i.InitFlashAddr) refers to bsp_flash.o(.data) for .data
    bsp_flash.o(i.ReadFlash) refers to bsp_flash.o(.data) for .data
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(i.WriteOld) for WriteOld
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(i.ReadFlashByAddr) for ReadFlashByAddr
    bsp_flash.o(i.WriteFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_flash.o(i.WriteFlash) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(.data) for .data
    bsp_flash.o(i.WriteFlash) refers to bsp_flash.o(.bss) for .bss
    bsp_flash.o(i.WriteOld) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_flash.o(i.WriteOld) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_flash.o(i.WriteOld) refers to bsp_flash.o(.bss) for .bss
    bsp_flash.o(i.WriteOld) refers to bsp_flash.o(.data) for .data
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_exmc.o(i.exmc_norsram_init) for exmc_norsram_init
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to gd32f4xx_exmc.o(i.exmc_norsram_enable) for exmc_norsram_enable
    bsp_fmc.o(i.exmc_asynchronous_sram_init) refers to systick.o(i.delay_ms) for delay_ms
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_init) for exmc_sdram_init
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_flag_get) for exmc_flag_get
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_command_config) for exmc_sdram_command_config
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to systick.o(i.delay_ms) for delay_ms
    bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init) refers to gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set) for exmc_sdram_refresh_count_set
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_erase_sector_by_address) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_sector_info_get) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_16bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_16bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_16bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_halfword_program) for fmc_halfword_program
    bsp_fmc.o(i.fmc_write_16bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_32bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_32bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_32bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    bsp_fmc.o(i.fmc_write_32bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_flag_clear) for fmc_flag_clear
    bsp_fmc.o(i.fmc_write_8bit_data) refers to bsp_fmc.o(i.fmc_sector_info_get) for fmc_sector_info_get
    bsp_fmc.o(i.fmc_write_8bit_data) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_fmc.o(i.fmc_write_8bit_data) refers to bsp_fmc.o(i.sector_name_to_number) for sector_name_to_number
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_byte_program) for fmc_byte_program
    bsp_fmc.o(i.fmc_write_8bit_data) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    bsp_fwdgt.o(i.bsp_fwdgt_feed) refers to gd32f4xx_fwdgt.o(i.fwdgt_counter_reload) for fwdgt_counter_reload
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_fwdgt.o(i.fwdgt_config) for fwdgt_config
    bsp_fwdgt.o(i.bsp_fwdgt_init) refers to gd32f4xx_fwdgt.o(i.fwdgt_enable) for fwdgt_enable
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_gpio.o(i.bsp_gpio_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    bsp_i2c.o(i.bsp_i2c_init) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    bsp_i2c.o(i.i2c_bus_reset) refers to gd32f4xx_i2c.o(i.i2c_deinit) for i2c_deinit
    bsp_i2c.o(i.i2c_bus_reset) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_i2c.o(i.i2c_bus_reset) refers to bsp_i2c.o(i.bsp_i2c_init) for bsp_i2c_init
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_ackpos_config) for i2c_ackpos_config
    bsp_i2c.o(i.i2c_read_timeout) refers to bsp_i2c.o(i.i2c_bus_reset) for i2c_bus_reset
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_data_transmit) for i2c_data_transmit
    bsp_i2c.o(i.i2c_read_timeout) refers to gd32f4xx_i2c.o(i.i2c_data_receive) for i2c_data_receive
    bsp_i2c.o(i.i2c_write_timeout) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    bsp_i2c.o(i.i2c_write_timeout) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    bsp_i2c.o(i.i2c_write_timeout) refers to bsp_i2c.o(i.i2c_bus_reset) for i2c_bus_reset
    bsp_i2c.o(i.i2c_write_timeout) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    bsp_i2c.o(i.i2c_write_timeout) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    bsp_i2c.o(i.i2c_write_timeout) refers to gd32f4xx_i2c.o(i.i2c_data_transmit) for i2c_data_transmit
    bsp_i2c.o(i.i2c_write_timeout) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_get) for rtc_flag_get
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to bsp_rtc.o(i.rtc_show_timestamp) for rtc_show_timestamp
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) refers to bsp_rtc.o(.data) for .data
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to bsp_rtc.o(i.rtc_pre_config) for rtc_pre_config
    bsp_rtc.o(i.bsp_rtc_init) refers to bsp_rtc.o(i.rtc_setup) for rtc_setup
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_timestamp_enable) for rtc_timestamp_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_interrupt_enable) for rtc_interrupt_enable
    bsp_rtc.o(i.bsp_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_flag_clear) for rtc_flag_clear
    bsp_rtc.o(i.getRTCWeekSecond) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    bsp_rtc.o(i.getRTCWeekSecond) refers to bsp_rtc.o(i.yearDay) for yearDay
    bsp_rtc.o(i.getRTCWeekSecond) refers to mktime.o(.text) for mktime
    bsp_rtc.o(i.getRTCWeekSecond) refers to gd32f4xx_rtc.o(i.rtc_subsecond_get) for rtc_subsecond_get
    bsp_rtc.o(i.getRTCWeekSecond) refers to f2d.o(.text) for __aeabi_f2d
    bsp_rtc.o(i.getRTCWeekSecond) refers to time_unify.o(i.time2gpst) for time2gpst
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_rtc.o(i.rtc_pre_config) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    bsp_rtc.o(i.rtc_setup) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    bsp_rtc.o(i.rtc_show_timestamp) refers to memseta.o(.text) for __aeabi_memclr
    bsp_rtc.o(i.rtc_show_timestamp) refers to gd32f4xx_rtc.o(i.rtc_timestamp_get) for rtc_timestamp_get
    bsp_rtc.o(i.rtc_show_timestamp) refers to gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get) for rtc_timestamp_subsecond_get
    bsp_rtc.o(i.rtc_show_timestamp) refers to printfa.o(i.__0sprintf) for __2sprintf
    bsp_rtc.o(i.timeSync) refers to time_unify.o(i.gpst2time) for gpst2time
    bsp_rtc.o(i.timeSync) refers to time_unify.o(i.gpst2utc) for gpst2utc
    bsp_rtc.o(i.timeSync) refers to localtime_w.o(.text) for localtime
    bsp_rtc.o(i.timeSync) refers to memcpya.o(.text) for __aeabi_memcpy4
    bsp_rtc.o(i.timeSync) refers to bsp_rtc.o(i.rtc_setup) for rtc_setup
    bsp_rtc.o(i.yearDay) refers to bsp_rtc.o(i.isLeapYear) for isLeapYear
    bsp_rtc.o(.data) refers to bsp_rtc.o(.data) for gRTC
    bsp_rtc.o(.data) refers to bsp_rtc.o(.bss) for gTimeStamp
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config) for rcu_timer_clock_prescaler_config
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    bsp_tim.o(i.bsp_tim_init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    bsp_uart.o(i.bsp_systick_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    ch378_hal.o(i.CH378_mDelaymS) refers to systick.o(i.delay_ms) for delay_ms
    ch378_hal.o(i.CH378_mDelayuS) refers to systick.o(i.delay_us) for delay_us
    ch378_hal.o(i.mStopIfError) refers to printfa.o(i.__0printf) for __2printf
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDCheckExist) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetGlobIntStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetMACAddr) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetPHYStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDGetRemoteIPP) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDGetSocketStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetUnreachIPPT) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetUnreachIPPT) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDGetVer) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDGetVer) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CMDInitCH395) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395CMDReset) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetGWIPAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetIPAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMACAddr) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CMDSetMACFilt) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMACFilt) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetMASKAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetPHY) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetPHY) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetRetryCount) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetRetryCount) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetRetryPeriod) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetRetryPeriod) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CMDSetUartBaudRate) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CMDSleep) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395ClearRecvBuf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395ClearRecvBuf) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CloseSocket) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395CloseSocket) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395CloseSocket) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395CloseSocket) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395DHCPEnable) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395EEPROMErase) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EEPROMErase) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395EEPROMErase) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395EEPROMRead) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395EEPROMWrite) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395EnablePing) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395EnablePing) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetCmdStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetCmdStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetDHCPStatus) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetDHCPStatus) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetIPInf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetIPInf) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395cmd.o(i.CH395GetRecvData) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetRecvLength) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetRecvLength) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetRecvLength) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.mDelayuS) for mDelayuS
    ch395cmd.o(i.CH395GetSocketInt) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395OpenSocket) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395OpenSocket) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395OpenSocket) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395OpenSocket) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395ReadGPIOAddr) refers to ch395spi.o(i.xReadCH395Data) for xReadCH395Data
    ch395cmd.o(i.CH395SendData) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SendData) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketDesIP) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketDesIP) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketDesPort) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketDesPort) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketIPRAWProto) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketIPRAWProto) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketProtType) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketProtType) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketRecvBuf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketRecvBuf) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketSendBuf) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketSendBuf) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetSocketSourPort) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetSocketSourPort) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetStartPara) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetStartPara) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395SetTCPMss) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395SetTCPMss) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPConnect) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395TCPConnect) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPConnect) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395TCPConnect) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395TCPDisconnect) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395TCPListen) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395TCPListen) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395cmd.o(i.CH395TCPListen) refers to ch395spi.o(i.mDelaymS) for mDelaymS
    ch395cmd.o(i.CH395TCPListen) refers to ch395cmd.o(i.CH395GetCmdStatus) for CH395GetCmdStatus
    ch395cmd.o(i.CH395UDPSendTo) refers to ch395cmd.o(i.CH395SetSocketDesIP) for CH395SetSocketDesIP
    ch395cmd.o(i.CH395UDPSendTo) refers to ch395cmd.o(i.CH395SetSocketDesPort) for CH395SetSocketDesPort
    ch395cmd.o(i.CH395UDPSendTo) refers to ch395cmd.o(i.CH395SendData) for CH395SendData
    ch395cmd.o(i.CH395WriteGPIOAddr) refers to ch395spi.o(i.xWriteCH395Cmd) for xWriteCH395Cmd
    ch395cmd.o(i.CH395WriteGPIOAddr) refers to ch395spi.o(i.xWriteCH395Data) for xWriteCH395Data
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    ch395spi.o(i.CH395_PORT_INIT) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    ch395spi.o(i.CH395_RST) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.CH395_RST) refers to systick.o(i.delay_ms) for delay_ms
    ch395spi.o(i.CH395_RST) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.Query395Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch395spi.o(i.Spi395Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch395spi.o(i.mDelaymS) refers to systick.o(i.delay_ms) for delay_ms
    ch395spi.o(i.mDelayuS) refers to systick.o(i.delay_us) for delay_us
    ch395spi.o(i.xReadCH395Data) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    ch395spi.o(i.xWriteCH395Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch395spi.o(i.xWriteCH395Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch395spi.o(i.xWriteCH395Cmd) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    ch395spi.o(i.xWriteCH395Cmd) refers to systick.o(i.delay_us) for delay_us
    ch395spi.o(i.xWriteCH395Data) refers to ch395spi.o(i.Spi395Exchange) for Spi395Exchange
    common.o(i.bmp2_delay_us) refers to systick.o(i.delay_us) for delay_us
    common.o(i.bmp2_i2c_read) refers to bsp_i2c.o(i.i2c_read_timeout) for i2c_read_timeout
    common.o(i.bmp2_i2c_read) refers to common.o(.data) for .data
    common.o(i.bmp2_i2c_write) refers to bsp_i2c.o(i.i2c_write_timeout) for i2c_write_timeout
    common.o(i.bmp2_i2c_write) refers to common.o(.data) for .data
    common.o(i.bmp2_interface_selection) refers to bsp_i2c.o(i.bsp_i2c_init) for bsp_i2c_init
    common.o(i.bmp2_interface_selection) refers to systick.o(i.delay_ms) for delay_ms
    common.o(i.bmp2_interface_selection) refers to common.o(.data) for .data
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_i2c_read) for bmp2_i2c_read
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_i2c_write) for bmp2_i2c_write
    common.o(i.bmp2_interface_selection) refers to common.o(i.bmp2_delay_us) for bmp2_delay_us
    file_sys.o(i.CH378AutoEnumDevice) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378AutoInitDisk) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378AutoResetDisk) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378BlockOnlyCMD) refers to file_sys.o(i.CH378WriteOfsBlock) for CH378WriteOfsBlock
    file_sys.o(i.CH378BlockOnlyCMD) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378BlockOnlyCMD) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378ByteLocate) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteLocate) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteLocate) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteLocate) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteRead) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteRead) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteRead) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteRead) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteRead) refers to file_sys.o(i.CH378GetTrueLen) for CH378GetTrueLen
    file_sys.o(i.CH378ByteRead) refers to file_sys.o(i.CH378ReadBlock) for CH378ReadBlock
    file_sys.o(i.CH378ByteReadPrepare) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteReadPrepare) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteReadPrepare) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteReadPrepare) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteReadPrepare) refers to file_sys.o(i.CH378GetTrueLen) for CH378GetTrueLen
    file_sys.o(i.CH378ByteWrite) refers to file_sys.o(i.CH378WriteOfsBlock) for CH378WriteOfsBlock
    file_sys.o(i.CH378ByteWrite) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteWrite) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteWrite) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteWrite) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378ByteWriteExecute) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ByteWriteExecute) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ByteWriteExecute) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ByteWriteExecute) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378CheckExist) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378CheckExist) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378CheckExist) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378CheckExist) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ClearStall) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378DirCreate) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378DirCreate) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DirInfoRead) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378DirInfoSave) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378DiskCapacity) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskCapacity) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskCapacity) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378DiskCapacity) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378DiskConnect) refers to ch378_spi_hw.o(i.Query378Interrupt) for Query378Interrupt
    file_sys.o(i.CH378DiskConnect) refers to file_sys.o(i.CH378GetIntStatus) for CH378GetIntStatus
    file_sys.o(i.CH378DiskConnect) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskQuery) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskQuery) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskQuery) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378DiskQuery) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378DiskReadSec) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskReadSec) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378DiskReadSec) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378DiskReadSec) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378DiskReadSec) refers to file_sys.o(i.CH378ReadBlock) for CH378ReadBlock
    file_sys.o(i.CH378DiskReadSec) refers to file_sys.o(.data) for .data
    file_sys.o(i.CH378DiskReady) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378DiskWriteSec) refers to file_sys.o(i.CH378WriteOfsBlock) for CH378WriteOfsBlock
    file_sys.o(i.CH378DiskWriteSec) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378DiskWriteSec) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378DiskWriteSec) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378DiskWriteSec) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378DiskWriteSec) refers to file_sys.o(.data) for .data
    file_sys.o(i.CH378EnterFullSleep) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378EnterFullSleep) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378EnterFullSleep) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378EnterFullSleep) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    file_sys.o(i.CH378EnterHalfSleep) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378EnterHalfSleep) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378EnterHalfSleep) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378EnterHalfSleep) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    file_sys.o(i.CH378FileClose) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378FileCreate) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378FileCreate) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileErase) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378FileErase) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileModify) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378FileModify) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378FileModify) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378FileModify) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileOpen) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    file_sys.o(i.CH378FileOpen) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileQuery) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378FileQuery) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskInquiry) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskInquiry) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskReady) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskReqSense) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskReqSense) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskSize) refers to file_sys.o(i.CH378SendCmdWaitInt) for CH378SendCmdWaitInt
    file_sys.o(i.CH378GetDiskSize) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378GetDiskStatus) refers to file_sys.o(i.CH378ReadVar8) for CH378ReadVar8
    file_sys.o(i.CH378GetFileSize) refers to file_sys.o(i.CH378ReadVar32) for CH378ReadVar32
    file_sys.o(i.CH378GetICVer) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetICVer) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetICVer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378GetIntStatus) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetIntStatus) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetIntStatus) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378GetTrueLen) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378GetTrueLen) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378GetTrueLen) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378HardwareReset) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378HardwareReset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378HardwareReset) refers to ch378_hal.o(i.CH378_mDelaymS) for CH378_mDelaymS
    file_sys.o(i.CH378Read32bitDat) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378Read32bitDat) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378ReadBlock) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378ReadOfsBlock) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadOfsBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadReqBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadReqBlock) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadReqBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378ReadReqBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378ReadVar32) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar32) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar32) refers to file_sys.o(i.CH378Read32bitDat) for CH378Read32bitDat
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378ReadVar8) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378ReadVar8) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecLocate) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SecLocate) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SecLocate) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecLocate) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SecRead) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SecRead) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SecRead) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecRead) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SecRead) refers to file_sys.o(i.CH378GetTrueLen) for CH378GetTrueLen
    file_sys.o(i.CH378SecRead) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378SecRead) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    file_sys.o(i.CH378SecRead) refers to file_sys.o(.data) for .data
    file_sys.o(i.CH378SecWrite) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SecWrite) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SecWrite) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SecWrite) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SecWrite) refers to file_sys.o(.data) for .data
    file_sys.o(i.CH378SelfEnumDevice) refers to file_sys.o(i.CH378SendCmdDatWaitInt) for CH378SendCmdDatWaitInt
    file_sys.o(i.CH378SelfEnumDevice) refers to file_sys.o(i.CH378ReadReqBlock) for CH378ReadReqBlock
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdDatWaitInt) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SendCmdWaitInt) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SendCmdWaitInt) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SendCmdWaitInt) refers to file_sys.o(i.Wait378Interrupt) for Wait378Interrupt
    file_sys.o(i.CH378SetFileName) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378SetFileName) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378SetFileName) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378SetFileSize) refers to file_sys.o(i.CH378WriteVar32) for CH378WriteVar32
    file_sys.o(i.CH378WriteBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378WriteBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteOfsBlock) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteOfsBlock) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    file_sys.o(i.CH378WriteOfsBlock) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteVar32) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar32) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar32) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.CH378WriteVar8) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    file_sys.o(i.CH378WriteVar8) refers to ch378_spi_hw.o(i.xWriteCH378Data) for xWriteCH378Data
    file_sys.o(i.CH378WriteVar8) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    file_sys.o(i.Wait378Interrupt) refers to ch378_spi_hw.o(i.Query378Interrupt) for Query378Interrupt
    file_sys.o(i.Wait378Interrupt) refers to file_sys.o(i.CH378GetIntStatus) for CH378GetIntStatus
    file_sys.o(i.Wait378Interrupt) refers to ch378_hal.o(i.CH378_mDelayuS) for CH378_mDelayuS
    logger.o(i.CopyAndConvertFile) refers to printfa.o(i.__0printf) for __2printf
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378FileOpen) for CH378FileOpen
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378ByteLocate) for CH378ByteLocate
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378ByteRead) for CH378ByteRead
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378GetFileSize) for CH378GetFileSize
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378ByteWrite) for CH378ByteWrite
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378FileClose) for CH378FileClose
    logger.o(i.CopyAndConvertFile) refers to file_sys.o(i.CH378FileCreate) for CH378FileCreate
    logger.o(i.CopyAndConvertFile) refers to logger.o(.bss) for .bss
    logger.o(i.generateCSVLogFileName) refers to memseta.o(.text) for __aeabi_memclr4
    logger.o(i.generateCSVLogFileName) refers to bsp_rtc.o(i.rtc_show_timestamp) for rtc_show_timestamp
    logger.o(i.generateCSVLogFileName) refers to printfa.o(i.__0sprintf) for __2sprintf
    logger.o(i.generateCSVLogFileName) refers to strcpy.o(.text) for strcpy
    logger.o(i.generateCSVLogFileName) refers to logger.o(.bss) for .bss
    logger.o(i.generateCSVLogFileName) refers to bsp_rtc.o(.data) for pRTC
    logger.o(i.parseFPGABuff) refers to dfltui.o(.text) for __aeabi_ui2d
    logger.o(i.parseFPGABuff) refers to dmul.o(.text) for __aeabi_dmul
    logger.o(i.parseFPGABuff) refers to d2f.o(.text) for __aeabi_d2f
    logger.o(i.parseFPGABuff) refers to f2d.o(.text) for __aeabi_f2d
    logger.o(i.parseFPGABuff) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    logger.o(i.parseFPGABuff) refers to ins_data.o(.data) for fpga_data_read_flag
    logger.o(i.parseFPGABuff) refers to ins_data.o(.bss) for hINSFPGAData
    logger.o(i.synthesisLogBuf) refers to memseta.o(.text) for __aeabi_memclr4
    logger.o(i.synthesisLogBuf) refers to time_unify.o(i.gpst2time) for gpst2time
    logger.o(i.synthesisLogBuf) refers to time_unify.o(i.gpst2utc) for gpst2utc
    logger.o(i.synthesisLogBuf) refers to logger.o(i.parseFPGABuff) for parseFPGABuff
    logger.o(i.synthesisLogBuf) refers to time_unify.o(i.time2str) for time2str
    logger.o(i.synthesisLogBuf) refers to f2d.o(.text) for __aeabi_f2d
    logger.o(i.synthesisLogBuf) refers to printfa.o(i.__0sprintf) for __2sprintf
    logger.o(i.synthesisLogBuf) refers to strlen.o(.text) for strlen
    logger.o(i.synthesisLogBuf) refers to logger.o(.bss) for .bss
    logger.o(i.synthesisLogBuf) refers to logger.o(.conststring) for .conststring
    logger.o(i.writeCSVFileHead) refers to memseta.o(.text) for __aeabi_memclr
    logger.o(i.writeCSVFileHead) refers to strcpy.o(.text) for strcpy
    logger.o(i.writeCSVFileHead) refers to strlen.o(.text) for strlen
    logger.o(i.writeCSVFileHead) refers to file_sys.o(i.CH378ByteWrite) for CH378ByteWrite
    logger.o(i.writeCSVFileHead) refers to logger.o(.bss) for .bss
    logger.o(i.writeCSVFileHead) refers to logger.o(.conststring) for .conststring
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378SetFileName) for CH378SetFileName
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378FileOpen) for CH378FileOpen
    logger.o(i.writeCSVLog) refers to printfa.o(i.__0printf) for __2printf
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378FileCreate) for CH378FileCreate
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378ByteLocate) for CH378ByteLocate
    logger.o(i.writeCSVLog) refers to file_sys.o(i.CH378ByteWrite) for CH378ByteWrite
    logger.o(i.writeCSVLog) refers to logger.o(i.writeCSVFileHead) for writeCSVFileHead
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    ch378_spi_hw.o(i.CH378_Port_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    ch378_spi_hw.o(i.Query378Interrupt) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    ch378_spi_hw.o(i.SPI_Exchange) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.CH378_Port_Init) for CH378_Port_Init
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xWriteCH378Cmd) for xWriteCH378Cmd
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.mInitCH378Host) refers to ch378_spi_hw.o(i.xReadCH378Data) for xReadCH378Data
    ch378_spi_hw.o(i.mInitCH378Host) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.mInitCH378Host) refers to systick.o(i.delay_ms) for delay_ms
    ch378_spi_hw.o(i.xReadCH378Data) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    ch378_spi_hw.o(i.xWriteCH378Cmd) refers to systick.o(i.delay_us) for delay_us
    ch378_spi_hw.o(i.xWriteCH378Data) refers to ch378_spi_hw.o(i.SPI_Exchange) for SPI_Exchange
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) for TAMPER_STAMP_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_can.o(i.CAN0_RX1_IRQHandler) for CAN0_RX1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI5_9_IRQHandler) for EXTI5_9_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) for TIMER0_UP_TIMER9_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to bsp_can.o(i.CAN1_RX1_IRQHandler) for CAN1_RX1_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for .data
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_200m_25m_hxtal) for system_clock_200m_25m_hxtal
    data_convert.o(i.GetDouble) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    data_convert.o(i.GetNumber) refers to atoi.o(.text) for atoi
    data_convert.o(i.dec2HexStr) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.dtoc) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.dtoc) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.dtoc) refers to strlen.o(.text) for strlen
    data_convert.o(i.dtoc) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.dtoc) refers to data_convert.o(.constdata) for .constdata
    data_convert.o(i.ftoa) refers to cdcmple.o(.text) for __aeabi_cdcmple
    data_convert.o(i.ftoa) refers to dfixi.o(.text) for __aeabi_d2iz
    data_convert.o(i.ftoa) refers to dflti.o(.text) for __aeabi_i2d
    data_convert.o(i.ftoa) refers to dadd.o(.text) for __aeabi_drsub
    data_convert.o(i.ftoa) refers to d2f.o(.text) for __aeabi_d2f
    data_convert.o(i.itoa) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.itoa) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.itoa) refers to strlen.o(.text) for strlen
    data_convert.o(i.itoa) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.itoc) refers to memseta.o(.text) for __aeabi_memclr4
    data_convert.o(i.itoc) refers to printfa.o(i.__0sprintf) for __2sprintf
    data_convert.o(i.itoc) refers to strlen.o(.text) for strlen
    data_convert.o(i.itoc) refers to memcpya.o(.text) for __aeabi_memcpy
    data_convert.o(i.str2bin) refers to malloc.o(i.malloc) for malloc
    data_convert.o(i.str2bin) refers to strncpy.o(.text) for strncpy
    data_convert.o(i.str2bin) refers to data_convert.o(i.strbin2u16) for strbin2u16
    data_convert.o(i.str2bin) refers to strlen.o(.text) for strlen
    data_convert.o(i.str2bin) refers to malloc.o(i.free) for free
    data_convert.o(i.strInStrCount) refers to strstr.o(.text) for strstr
    data_convert.o(i.strReplace) refers to strlen.o(.text) for strlen
    data_convert.o(i.strReplace) refers to malloc.o(i.malloc) for malloc
    data_convert.o(i.strReplace) refers to strncmp.o(.text) for strncmp
    data_convert.o(i.strReplace) refers to strncat.o(.text) for strncat
    data_convert.o(i.strReplace) refers to strcpy.o(.text) for strcpy
    data_convert.o(i.strReplace) refers to malloc.o(i.free) for free
    data_convert.o(i.strReplace) refers to strcat.o(.text) for strcat
    data_convert.o(i.strReverse) refers to strlen.o(.text) for strlen
    data_convert.o(i.strSplit) refers to strtok.o(.text) for strtok
    data_convert.o(i.strbin2u16) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetBytesInBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_GetKey) refers to segger_rtt.o(i.SEGGER_RTT_Read) for SEGGER_RTT_Read
    segger_rtt.o(i.SEGGER_RTT_HasData) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_HasDataUp) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_HasKey) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Init) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutChar) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_PutCharSkip) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_Read) refers to segger_rtt.o(i.SEGGER_RTT_ReadNoLock) for SEGGER_RTT_ReadNoLock
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_ReadUpBuffer) refers to segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) for SEGGER_RTT_ReadUpBufferNoLock
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_SetTerminal) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._PostTerminalSwitch) for _PostTerminalSwitch
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_TerminalOut) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i.SEGGER_RTT_WaitKey) refers to segger_rtt.o(i.SEGGER_RTT_GetKey) for SEGGER_RTT_GetKey
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(i.SEGGER_RTT_WriteNoLock) for SEGGER_RTT_WriteNoLock
    segger_rtt.o(i.SEGGER_RTT_Write) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(i._DoInit) for _DoInit
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) for SEGGER_RTT_WriteDownBufferNoLock
    segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._GetAvailWriteSpace) for _GetAvailWriteSpace
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteNoCheck) for _WriteNoCheck
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i.SEGGER_RTT_WriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to strlen.o(.text) for strlen
    segger_rtt.o(i.SEGGER_RTT_WriteString) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.bss) for .bss
    segger_rtt.o(i._DoInit) refers to segger_rtt.o(.constdata) for .constdata
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(i._WriteBlocking) for _WriteBlocking
    segger_rtt.o(i._PostTerminalSwitch) refers to segger_rtt.o(.data) for .data
    segger_rtt.o(i._WriteBlocking) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt.o(i._WriteNoCheck) refers to memcpya.o(.text) for __aeabi_memcpy
    segger_rtt_printf.o(i.SEGGER_RTT_printf) refers to segger_rtt_printf.o(i.SEGGER_RTT_vprintf) for SEGGER_RTT_vprintf
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintInt) for _PrintInt
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i.SEGGER_RTT_vprintf) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintInt) refers to segger_rtt_printf.o(i._PrintUnsigned) for _PrintUnsigned
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(i._StoreChar) for _StoreChar
    segger_rtt_printf.o(i._PrintUnsigned) refers to segger_rtt_printf.o(.constdata) for .constdata
    segger_rtt_printf.o(i._StoreChar) refers to segger_rtt.o(i.SEGGER_RTT_Write) for SEGGER_RTT_Write
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    exp.o(i.__hardfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp.o(i.__hardfp_exp) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp.o(i.__hardfp_exp) refers to errno.o(i.__set_errno) for __set_errno
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    exp.o(i.__hardfp_exp) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    exp.o(i.__hardfp_exp) refers to dadd.o(.text) for __aeabi_dsub
    exp.o(i.__hardfp_exp) refers to dmul.o(.text) for __aeabi_dmul
    exp.o(i.__hardfp_exp) refers to dfixi.o(.text) for __aeabi_d2iz
    exp.o(i.__hardfp_exp) refers to dflti.o(.text) for __aeabi_i2d
    exp.o(i.__hardfp_exp) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp.o(i.__hardfp_exp) refers to ddiv.o(.text) for __aeabi_ddiv
    exp.o(i.__hardfp_exp) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    exp.o(i.__hardfp_exp) refers to exp.o(.constdata) for .constdata
    exp.o(i.__softfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__softfp_exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(i.exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp_x.o(i.____hardfp_exp$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to errno.o(i.__set_errno) for __set_errno
    exp_x.o(i.____hardfp_exp$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to dadd.o(.text) for __aeabi_dsub
    exp_x.o(i.____hardfp_exp$lsc) refers to dmul.o(.text) for __aeabi_dmul
    exp_x.o(i.____hardfp_exp$lsc) refers to dfixi.o(.text) for __aeabi_d2iz
    exp_x.o(i.____hardfp_exp$lsc) refers to dflti.o(.text) for __aeabi_i2d
    exp_x.o(i.____hardfp_exp$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp_x.o(i.____hardfp_exp$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    exp_x.o(i.____hardfp_exp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    exp_x.o(i.____hardfp_exp$lsc) refers to exp_x.o(.constdata) for .constdata
    exp_x.o(i.____softfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____softfp_exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(i.__exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.__exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__hardfp_floor) refers to dadd.o(.text) for __aeabi_dadd
    floor.o(i.__hardfp_floor) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    floor.o(i.__softfp_floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    floor.o(i.floor) refers (Special) to iusefp.o(.text) for __I$use$fp
    floor.o(i.floor) refers to floor.o(i.__hardfp_floor) for __hardfp_floor
    fmod.o(i.__hardfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem.o(.text) for _drem
    fmod.o(i.__hardfp_fmod) refers to dadd.o(.text) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to errno.o(i.__set_errno) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod_x.o(i.____hardfp_fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.____hardfp_fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.____hardfp_fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.____hardfp_fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmod_x.o(i.____softfp_fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.____softfp_fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.____softfp_fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.____softfp_fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmod_x.o(i.__fmod$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod_x.o(i.__fmod$lsc) refers to drem.o(.text) for _drem
    fmod_x.o(i.__fmod$lsc) refers to dadd.o(.text) for __aeabi_drsub
    fmod_x.o(i.__fmod$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    localtime.o(.text) refers to localtime.o(.bss) for .bss
    localtime.o(.text) refers to localtime.o(.constdata) for .constdata
    mktime.o(.text) refers to localtime_i.o(.text) for _localtime
    mktime.o(.text) refers to mktime.o(.constdata) for .constdata
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to main.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to main.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to main.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to main.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to main.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to main.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to main.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to main.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to main.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to main.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to main.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    localtime_w.o(.text) refers to localtime_i.o(.text) for _localtime
    localtime_w.o(.text) refers to localtime_w.o(.bss) for .bss
    localtime_i.o(.text) refers to localtime_i.o(.constdata) for .constdata
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drem.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (30 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (80 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (32 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (62 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (32 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (84 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (28 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (78 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (44 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_channel_config), (126 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_data_read), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_software_startconv_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (32 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (32 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (32 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_regular_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (38 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (48 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (32 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (32 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (40 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (28 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (208 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (22 bytes).
    Removing gd32f4xx_can.o(i.can_init), (254 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (156 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (6 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (120 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (36 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (36 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (58 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (88 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (40 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (134 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (28 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (16 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (12 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (12 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (12 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (22 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (32 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (32 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (44 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_usbsof_signal_select), (24 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (44 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (16 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (32 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (28 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (28 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (16 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (22 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (44 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_disable), (18 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_enable), (18 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_subperipheral_select), (22 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (18 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (18 bytes).
    Removing gd32f4xx_dma.o(i.dma_deinit), (86 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_clear), (54 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_get), (56 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (26 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (30 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (30 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (54 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (196 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (18 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (26 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (144 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (30 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (12 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (46 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (132 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (24 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (28 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (28 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (12 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (12 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (22 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (92 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (26 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (84 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (136 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (160 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (144 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (124 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (704 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (228 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (208 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (164 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (192 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (180 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (188 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (92 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (76 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (16 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (10 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (46 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (26 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (134 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (26 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (90 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (26 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (136 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (248 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (62 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (30 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (10 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (10 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (18 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (18 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (24 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (20 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (104 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (56 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (56 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (48 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (52 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (92 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (80 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (92 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (60 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (44 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (64 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (64 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (80 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (20 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (104 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (184 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (60 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (34 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (22 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (84 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (18 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (22 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (12 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (128 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (76 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (240 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (128 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (76 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (16 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (22 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (16 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (16 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (24 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (24 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (24 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (24 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (22 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_enable), (148 bytes).
    Removing gd32f4xx_misc.o(i.nvic_priority_group_set), (20 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (22 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (32 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (40 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (40 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (184 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (16 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (16 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_clock_freq_get), (220 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (112 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (64 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (64 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (108 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (40 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (68 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (88 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (44 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (64 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (24 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (164 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (76 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (24 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (124 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (24 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (72 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (68 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (24 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (44 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (48 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (22 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (48 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (24 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (40 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (24 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (184 bytes).
    Removing gd32f4xx_spi.o(i.qspi_disable), (12 bytes).
    Removing gd32f4xx_spi.o(i.qspi_enable), (12 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_disable), (12 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_enable), (12 bytes).
    Removing gd32f4xx_spi.o(i.qspi_read_enable), (12 bytes).
    Removing gd32f4xx_spi.o(i.qspi_write_enable), (12 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (12 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (20 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (20 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (104 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (36 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (36 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (84 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (22 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (68 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (20 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (58 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (58 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (20 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (468 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (78 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (156 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (28 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (300 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (334 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (20 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (20 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (30 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (22 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (136 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (32 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (142 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (44 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (172 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_baudrate_set), (128 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (22 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_receive), (8 bytes).
    Removing gd32f4xx_usart.o(i.usart_deinit), (124 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (22 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_get), (48 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (76 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (22 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receive_config), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (18 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (30 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_transmit_config), (12 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (44 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (22 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (16 bytes).
    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(i.StartNavigation), (36 bytes).
    Removing main.o(i.StopNavigation), (2 bytes).
    Removing main.o(i.checkUSBReady), (46 bytes).
    Removing main.o(i.loggingLogFile), (40 bytes).
    Removing main.o(.data), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(i.NVIC_SetPriority), (32 bytes).
    Removing systick.o(i.delay_1ms), (16 bytes).
    Removing systick.o(i.delay_xms), (26 bytes).
    Removing systick.o(i.systick_config), (64 bytes).
    Removing can_data.o(.rev16_text), (4 bytes).
    Removing can_data.o(.revsh_text), (4 bytes).
    Removing can_data.o(.bss), (36 bytes).
    Removing gnss.o(.rev16_text), (4 bytes).
    Removing gnss.o(.revsh_text), (4 bytes).
    Removing gnss.o(i.GNSS_Buff_Parser), (56 bytes).
    Removing gnss.o(i.GNSS_Cmd_Kind_Parser), (22 bytes).
    Removing gnss.o(i.GNSS_Cmd_Parser), (324 bytes).
    Removing gnss.o(i.gnss_BESTPOS_Buff_Parser), (56 bytes).
    Removing gnss.o(i.gnss_Fetch_Data), (232 bytes).
    Removing gnss.o(i.gnss_GGA_Buff_Parser), (240 bytes).
    Removing gnss.o(i.gnss_Heading_Buff_Parser), (80 bytes).
    Removing gnss.o(i.gnss_RMC_Buff_Parser), (380 bytes).
    Removing gnss.o(i.gnss_TRA_Buff_Parser), (236 bytes).
    Removing gnss.o(i.gnss_VTG_Buff_Parser), (188 bytes).
    Removing gnss.o(i.gnss_ZDA_Buff_Parser), (264 bytes).
    Removing gnss.o(i.gnss_config_movingbase), (2 bytes).
    Removing gnss.o(i.gnss_request_GGA), (2 bytes).
    Removing gnss.o(i.gnss_request_GSA), (2 bytes).
    Removing gnss.o(i.gnss_request_HDT), (2 bytes).
    Removing gnss.o(i.gnss_request_HEADING), (2 bytes).
    Removing gnss.o(i.gnss_request_OTG), (2 bytes).
    Removing gnss.o(i.gnss_request_RMC), (2 bytes).
    Removing gnss.o(i.gnss_request_ZDA), (2 bytes).
    Removing gnss.o(i.gnss_request_saveconfig), (2 bytes).
    Removing gnss.o(.bss), (608 bytes).
    Removing gnss.o(.data), (2 bytes).
    Removing imu_data.o(.rev16_text), (4 bytes).
    Removing imu_data.o(.revsh_text), (4 bytes).
    Removing imu_data.o(.bss), (52 bytes).
    Removing ins_data.o(.rev16_text), (4 bytes).
    Removing ins_data.o(.revsh_text), (4 bytes).
    Removing ins_data.o(i.read_flash), (28 bytes).
    Removing ins_data.o(i.save_flash), (32 bytes).
    Removing ins_sys.o(.rev16_text), (4 bytes).
    Removing ins_sys.o(.revsh_text), (4 bytes).
    Removing ins_sys.o(.bss), (148 bytes).
    Removing ins_sys.o(.data), (4 bytes).
    Removing tcpserver.o(.rev16_text), (4 bytes).
    Removing tcpserver.o(.revsh_text), (4 bytes).
    Removing tcpserver.o(i.TCPServer_Process), (116 bytes).
    Removing tcpserver.o(i.ch395_socket_tcp_client_interrupt), (80 bytes).
    Removing tcpserver.o(.bss), (1520 bytes).
    Removing tcpserver.o(.data), (1 bytes).
    Removing time_unify.o(.rev16_text), (4 bytes).
    Removing time_unify.o(.revsh_text), (4 bytes).
    Removing time_unify.o(i.Bdt2UtcTime), (70 bytes).
    Removing time_unify.o(i.bdt2gpst), (28 bytes).
    Removing time_unify.o(i.bdt2time), (152 bytes).
    Removing time_unify.o(i.gpst2bdt), (28 bytes).
    Removing time_unify.o(i.gst2time), (152 bytes).
    Removing time_unify.o(i.str2time), (248 bytes).
    Removing time_unify.o(i.time2bdt), (80 bytes).
    Removing time_unify.o(i.time2epoch), (200 bytes).
    Removing time_unify.o(i.time2gst), (80 bytes).
    Removing time_unify.o(i.time2sec), (160 bytes).
    Removing time_unify.o(i.time2str), (280 bytes).
    Removing time_unify.o(i.utc2gmst), (404 bytes).
    Removing time_unify.o(i.utc2gpst), (160 bytes).
    Removing bmp2.o(i.bmp2_compensate_data), (76 bytes).
    Removing bmp2.o(i.bmp2_compute_meas_time), (80 bytes).
    Removing bmp2.o(i.bmp2_get_config), (82 bytes).
    Removing bmp2.o(i.bmp2_get_power_mode), (40 bytes).
    Removing bmp2.o(i.bmp2_get_regs), (66 bytes).
    Removing bmp2.o(i.bmp2_get_sensor_data), (160 bytes).
    Removing bmp2.o(i.bmp2_get_status), (50 bytes).
    Removing bmp2.o(i.bmp2_init), (50 bytes).
    Removing bmp2.o(i.bmp2_set_config), (10 bytes).
    Removing bmp2.o(i.bmp2_set_power_mode), (4 bytes).
    Removing bmp2.o(i.bmp2_set_regs), (156 bytes).
    Removing bmp2.o(i.bmp2_soft_reset), (28 bytes).
    Removing bmp2.o(i.compensate_pressure), (728 bytes).
    Removing bmp2.o(i.compensate_temperature), (420 bytes).
    Removing bmp2.o(i.conf_sensor), (148 bytes).
    Removing bmp2.o(i.get_calib_param), (114 bytes).
    Removing bmp2.o(i.null_ptr_check), (24 bytes).
    Removing bmp2.o(i.set_os_mode), (80 bytes).
    Removing bmp2.o(.constdata), (52 bytes).
    Removing bmp280.o(.rev16_text), (4 bytes).
    Removing bmp280.o(.revsh_text), (4 bytes).
    Removing bmp280.o(i.bmp280_get_data), (40 bytes).
    Removing bmp280.o(i.bmp280_init), (76 bytes).
    Removing bmp280.o(.bss), (60 bytes).
    Removing bmp280.o(.bss), (16 bytes).
    Removing bmp280.o(.data), (6 bytes).
    Removing bsp_adc.o(.rev16_text), (4 bytes).
    Removing bsp_adc.o(.revsh_text), (4 bytes).
    Removing bsp_adc.o(i.adc_config), (192 bytes).
    Removing bsp_adc.o(i.enter_sleep_mode), (18 bytes).
    Removing bsp_can.o(.rev16_text), (4 bytes).
    Removing bsp_can.o(.revsh_text), (4 bytes).
    Removing bsp_can.o(i.bsp_can_init), (152 bytes).
    Removing bsp_can.o(i.bsp_can_transmit), (40 bytes).
    Removing bsp_can.o(i.can_transmit_acc), (132 bytes).
    Removing bsp_can.o(i.can_transmit_angle), (132 bytes).
    Removing bsp_can.o(i.can_transmit_gyro), (132 bytes).
    Removing bsp_exti.o(.rev16_text), (4 bytes).
    Removing bsp_exti.o(.revsh_text), (4 bytes).
    Removing bsp_exti.o(i.bsp_exti_config), (68 bytes).
    Removing bsp_exti.o(i.bsp_exti_init), (32 bytes).
    Removing bsp_exti.o(.data), (72 bytes).
    Removing bsp_flash.o(.rev16_text), (4 bytes).
    Removing bsp_flash.o(.revsh_text), (4 bytes).
    Removing bsp_flash.o(i.EndWrite), (28 bytes).
    Removing bsp_fmc.o(.rev16_text), (4 bytes).
    Removing bsp_fmc.o(.revsh_text), (4 bytes).
    Removing bsp_fmc.o(i.FMC_ReadBuffer), (46 bytes).
    Removing bsp_fmc.o(i.FMC_ReadWord), (22 bytes).
    Removing bsp_fmc.o(i.FMC_WriteBuffer), (46 bytes).
    Removing bsp_fmc.o(i.FMC_WriteWord), (22 bytes).
    Removing bsp_fmc.o(i.exmc_synchronous_dynamic_ram_init), (596 bytes).
    Removing bsp_fmc.o(i.fill_buffer), (20 bytes).
    Removing bsp_fmc.o(i.fmc_erase_sector_by_address), (62 bytes).
    Removing bsp_fmc.o(i.fmc_read_16bit_data), (24 bytes).
    Removing bsp_fmc.o(i.fmc_read_32bit_data), (22 bytes).
    Removing bsp_fmc.o(i.fmc_read_8bit_data), (22 bytes).
    Removing bsp_fmc.o(i.fmc_sector_info_get), (296 bytes).
    Removing bsp_fmc.o(i.fmc_write_16bit_data), (118 bytes).
    Removing bsp_fmc.o(i.fmc_write_32bit_data), (118 bytes).
    Removing bsp_fmc.o(i.fmc_write_8bit_data), (116 bytes).
    Removing bsp_fmc.o(i.sdram_readbuffer_8), (34 bytes).
    Removing bsp_fmc.o(i.sdram_writebuffer_8), (34 bytes).
    Removing bsp_fmc.o(i.sector_name_to_number), (26 bytes).
    Removing bsp_fwdgt.o(.rev16_text), (4 bytes).
    Removing bsp_fwdgt.o(.revsh_text), (4 bytes).
    Removing bsp_fwdgt.o(i.bsp_fwdgt_feed), (4 bytes).
    Removing bsp_fwdgt.o(i.bsp_fwdgt_init), (38 bytes).
    Removing bsp_gpio.o(.rev16_text), (4 bytes).
    Removing bsp_gpio.o(.revsh_text), (4 bytes).
    Removing bsp_i2c.o(.rev16_text), (4 bytes).
    Removing bsp_i2c.o(.revsh_text), (4 bytes).
    Removing bsp_i2c.o(i.bsp_i2c_init), (144 bytes).
    Removing bsp_i2c.o(i.i2c_bus_reset), (136 bytes).
    Removing bsp_i2c.o(i.i2c_read_timeout), (464 bytes).
    Removing bsp_i2c.o(i.i2c_write_timeout), (344 bytes).
    Removing bsp_i2c.o(.data), (1 bytes).
    Removing bsp_rtc.o(.rev16_text), (4 bytes).
    Removing bsp_rtc.o(.revsh_text), (4 bytes).
    Removing bsp_sys.o(.rev16_text), (4 bytes).
    Removing bsp_sys.o(.revsh_text), (4 bytes).
    Removing bsp_sys.o(.emb_text), (16 bytes).
    Removing bsp_sys.o(i.GetSoftwareVersion), (6 bytes).
    Removing bsp_sys.o(i.IsDCahceEnable), (16 bytes).
    Removing bsp_sys.o(i.IsICahceEnable), (16 bytes).
    Removing bsp_sys.o(i.Sys_Soft_Reset), (16 bytes).
    Removing bsp_tim.o(.rev16_text), (4 bytes).
    Removing bsp_tim.o(.revsh_text), (4 bytes).
    Removing bsp_uart.o(.rev16_text), (4 bytes).
    Removing bsp_uart.o(.revsh_text), (4 bytes).
    Removing bsp_uart.o(i.bsp_systick_init), (152 bytes).
    Removing ch378_hal.o(.rev16_text), (4 bytes).
    Removing ch378_hal.o(.revsh_text), (4 bytes).
    Removing ch378_hal.o(i.CH378_mDelaymS), (4 bytes).
    Removing ch378_hal.o(i.mStopIfError), (36 bytes).
    Removing ch395cmd.o(.rev16_text), (4 bytes).
    Removing ch395cmd.o(.revsh_text), (4 bytes).
    Removing ch395cmd.o(i.CH395CMDCheckExist), (24 bytes).
    Removing ch395cmd.o(i.CH395CMDGetGlobIntStatus), (16 bytes).
    Removing ch395cmd.o(i.CH395CMDGetGlobIntStatus_ALL), (32 bytes).
    Removing ch395cmd.o(i.CH395CMDGetMACAddr), (30 bytes).
    Removing ch395cmd.o(i.CH395CMDGetPHYStatus), (16 bytes).
    Removing ch395cmd.o(i.CH395CMDGetRemoteIPP), (38 bytes).
    Removing ch395cmd.o(i.CH395CMDGetSocketStatus), (32 bytes).
    Removing ch395cmd.o(i.CH395CMDGetUnreachIPPT), (30 bytes).
    Removing ch395cmd.o(i.CH395CMDGetVer), (16 bytes).
    Removing ch395cmd.o(i.CH395CMDReset), (6 bytes).
    Removing ch395cmd.o(i.CH395CMDSetGWIPAddr), (30 bytes).
    Removing ch395cmd.o(i.CH395CMDSetIPAddr), (30 bytes).
    Removing ch395cmd.o(i.CH395CMDSetMACAddr), (38 bytes).
    Removing ch395cmd.o(i.CH395CMDSetMACFilt), (80 bytes).
    Removing ch395cmd.o(i.CH395CMDSetMASKAddr), (30 bytes).
    Removing ch395cmd.o(i.CH395CMDSetPHY), (20 bytes).
    Removing ch395cmd.o(i.CH395CMDSetRetryCount), (20 bytes).
    Removing ch395cmd.o(i.CH395CMDSetRetryPeriod), (26 bytes).
    Removing ch395cmd.o(i.CH395CMDSetUartBaudRate), (36 bytes).
    Removing ch395cmd.o(i.CH395CMDSleep), (6 bytes).
    Removing ch395cmd.o(i.CH395CRCRet6Bit), (52 bytes).
    Removing ch395cmd.o(i.CH395ClearRecvBuf), (20 bytes).
    Removing ch395cmd.o(i.CH395CloseSocket), (46 bytes).
    Removing ch395cmd.o(i.CH395DHCPEnable), (46 bytes).
    Removing ch395cmd.o(i.CH395EEPROMErase), (24 bytes).
    Removing ch395cmd.o(i.CH395EEPROMRead), (56 bytes).
    Removing ch395cmd.o(i.CH395EEPROMWrite), (60 bytes).
    Removing ch395cmd.o(i.CH395EnablePing), (20 bytes).
    Removing ch395cmd.o(i.CH395GetDHCPStatus), (16 bytes).
    Removing ch395cmd.o(i.CH395GetIPInf), (30 bytes).
    Removing ch395cmd.o(i.CH395GetRecvData), (62 bytes).
    Removing ch395cmd.o(i.CH395GetRecvLength), (34 bytes).
    Removing ch395cmd.o(i.CH395GetSocketInt), (30 bytes).
    Removing ch395cmd.o(i.CH395ReadGPIOAddr), (30 bytes).
    Removing ch395cmd.o(i.CH395SendData), (54 bytes).
    Removing ch395cmd.o(i.CH395SetSocketDesIP), (46 bytes).
    Removing ch395cmd.o(i.CH395SetSocketDesPort), (34 bytes).
    Removing ch395cmd.o(i.CH395SetSocketIPRAWProto), (28 bytes).
    Removing ch395cmd.o(i.CH395SetTCPMss), (26 bytes).
    Removing ch395cmd.o(i.CH395TCPConnect), (46 bytes).
    Removing ch395cmd.o(i.CH395TCPDisconnect), (46 bytes).
    Removing ch395cmd.o(i.CH395UDPSendTo), (42 bytes).
    Removing ch395cmd.o(i.CH395WriteGPIOAddr), (28 bytes).
    Removing ch395spi.o(.rev16_text), (4 bytes).
    Removing ch395spi.o(.revsh_text), (4 bytes).
    Removing ch395spi.o(i.Query395Interrupt), (28 bytes).
    Removing ch395spi.o(i.mDelayuS), (4 bytes).
    Removing common.o(.rev16_text), (4 bytes).
    Removing common.o(.revsh_text), (4 bytes).
    Removing common.o(i.bmp2_delay_us), (4 bytes).
    Removing common.o(i.bmp2_i2c_read), (28 bytes).
    Removing common.o(i.bmp2_i2c_write), (28 bytes).
    Removing common.o(i.bmp2_interface_selection), (76 bytes).
    Removing common.o(.data), (1 bytes).
    Removing file_sys.o(.rev16_text), (4 bytes).
    Removing file_sys.o(.revsh_text), (4 bytes).
    Removing file_sys.o(i.CH378AutoEnumDevice), (6 bytes).
    Removing file_sys.o(i.CH378AutoInitDisk), (6 bytes).
    Removing file_sys.o(i.CH378AutoResetDisk), (6 bytes).
    Removing file_sys.o(i.CH378BlockOnlyCMD), (36 bytes).
    Removing file_sys.o(i.CH378ByteRead), (76 bytes).
    Removing file_sys.o(i.CH378ByteReadPrepare), (68 bytes).
    Removing file_sys.o(i.CH378ByteWriteExecute), (64 bytes).
    Removing file_sys.o(i.CH378CheckExist), (48 bytes).
    Removing file_sys.o(i.CH378ClearStall), (8 bytes).
    Removing file_sys.o(i.CH378DirCreate), (16 bytes).
    Removing file_sys.o(i.CH378DirInfoRead), (8 bytes).
    Removing file_sys.o(i.CH378DirInfoSave), (8 bytes).
    Removing file_sys.o(i.CH378DiskCapacity), (42 bytes).
    Removing file_sys.o(i.CH378DiskConnect), (22 bytes).
    Removing file_sys.o(i.CH378DiskQuery), (112 bytes).
    Removing file_sys.o(i.CH378DiskReadSec), (152 bytes).
    Removing file_sys.o(i.CH378DiskReady), (6 bytes).
    Removing file_sys.o(i.CH378DiskWriteSec), (148 bytes).
    Removing file_sys.o(i.CH378EnterFullSleep), (40 bytes).
    Removing file_sys.o(i.CH378EnterHalfSleep), (40 bytes).
    Removing file_sys.o(i.CH378FileClose), (8 bytes).
    Removing file_sys.o(i.CH378FileErase), (16 bytes).
    Removing file_sys.o(i.CH378FileModify), (124 bytes).
    Removing file_sys.o(i.CH378FileQuery), (24 bytes).
    Removing file_sys.o(i.CH378GetDiskInquiry), (28 bytes).
    Removing file_sys.o(i.CH378GetDiskReady), (6 bytes).
    Removing file_sys.o(i.CH378GetDiskReqSense), (28 bytes).
    Removing file_sys.o(i.CH378GetDiskSize), (28 bytes).
    Removing file_sys.o(i.CH378GetDiskStatus), (6 bytes).
    Removing file_sys.o(i.CH378GetFileSize), (6 bytes).
    Removing file_sys.o(i.CH378GetICVer), (32 bytes).
    Removing file_sys.o(i.CH378GetTrueLen), (64 bytes).
    Removing file_sys.o(i.CH378HardwareReset), (32 bytes).
    Removing file_sys.o(i.CH378Read32bitDat), (60 bytes).
    Removing file_sys.o(i.CH378ReadBlock), (68 bytes).
    Removing file_sys.o(i.CH378ReadOfsBlock), (84 bytes).
    Removing file_sys.o(i.CH378ReadReqBlock), (72 bytes).
    Removing file_sys.o(i.CH378ReadVar32), (24 bytes).
    Removing file_sys.o(i.CH378ReadVar8), (40 bytes).
    Removing file_sys.o(i.CH378SecLocate), (60 bytes).
    Removing file_sys.o(i.CH378SecRead), (296 bytes).
    Removing file_sys.o(i.CH378SecWrite), (288 bytes).
    Removing file_sys.o(i.CH378SelfEnumDevice), (66 bytes).
    Removing file_sys.o(i.CH378SendCmdDatWaitInt), (36 bytes).
    Removing file_sys.o(i.CH378SetFileSize), (8 bytes).
    Removing file_sys.o(i.CH378WriteBlock), (68 bytes).
    Removing file_sys.o(i.CH378WriteVar32), (64 bytes).
    Removing file_sys.o(i.CH378WriteVar8), (44 bytes).
    Removing file_sys.o(.data), (2 bytes).
    Removing logger.o(.rev16_text), (4 bytes).
    Removing logger.o(.revsh_text), (4 bytes).
    Removing logger.o(i.CopyAndConvertFile), (336 bytes).
    Removing logger.o(i.SDFile2USB), (2 bytes).
    Removing logger.o(i.parseFPGABuff), (1040 bytes).
    Removing logger.o(i.synthesisLogBuf), (568 bytes).
    Removing ch378_spi_hw.o(.rev16_text), (4 bytes).
    Removing ch378_spi_hw.o(.revsh_text), (4 bytes).
    Removing startup_gd32f450_470.o(HEAP), (1024 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (144 bytes).
    Removing system_gd32f4xx.o(.data), (4 bytes).
    Removing data_convert.o(i.GetDouble), (74 bytes).
    Removing data_convert.o(i.GetNumber), (72 bytes).
    Removing data_convert.o(i.InvertUint16), (40 bytes).
    Removing data_convert.o(i.InvertUint32), (40 bytes).
    Removing data_convert.o(i.InvertUint8), (40 bytes).
    Removing data_convert.o(i.SignedToBuf), (36 bytes).
    Removing data_convert.o(i.UnsignedToBuf), (36 bytes).
    Removing data_convert.o(i.bufToFloat), (38 bytes).
    Removing data_convert.o(i.bufToSigned), (36 bytes).
    Removing data_convert.o(i.bufToU32), (40 bytes).
    Removing data_convert.o(i.bufToUnsigned), (36 bytes).
    Removing data_convert.o(i.char2strbin), (56 bytes).
    Removing data_convert.o(i.data24BitToSigned), (10 bytes).
    Removing data_convert.o(i.dec2HexStr), (24 bytes).
    Removing data_convert.o(i.double2Hex), (26 bytes).
    Removing data_convert.o(i.dtoc), (104 bytes).
    Removing data_convert.o(i.float2Hex), (26 bytes).
    Removing data_convert.o(i.floatToBuf), (38 bytes).
    Removing data_convert.o(i.ftoa), (268 bytes).
    Removing data_convert.o(i.hex2Double), (16 bytes).
    Removing data_convert.o(i.hex2Float), (8 bytes).
    Removing data_convert.o(i.int2strbin), (50 bytes).
    Removing data_convert.o(i.itoa), (72 bytes).
    Removing data_convert.o(i.itoc), (100 bytes).
    Removing data_convert.o(i.short2strbin), (56 bytes).
    Removing data_convert.o(i.str2bin), (84 bytes).
    Removing data_convert.o(i.str2lower), (34 bytes).
    Removing data_convert.o(i.strInStrCount), (26 bytes).
    Removing data_convert.o(i.strReplace), (110 bytes).
    Removing data_convert.o(i.strReverse), (44 bytes).
    Removing data_convert.o(i.strSplit), (48 bytes).
    Removing data_convert.o(i.strbin2u16), (42 bytes).
    Removing data_convert.o(i.strt2upper), (34 bytes).
    Removing data_convert.o(i.u32ToBuf), (34 bytes).
    Removing data_convert.o(.constdata), (8 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocDownBuffer), (104 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_AllocUpBuffer), (104 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer), (92 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer), (92 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetAvailWriteSpace), (20 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetBytesInBuffer), (36 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_GetKey), (28 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasData), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasDataUp), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_HasKey), (36 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Init), (4 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutChar), (96 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkip), (84 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_PutCharSkipNoLock), (52 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Read), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadNoLock), (124 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadUpBuffer), (24 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_ReadUpBufferNoLock), (124 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsDownBuffer), (64 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetFlagsUpBuffer), (64 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameDownBuffer), (64 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetNameUpBuffer), (64 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_SetTerminal), (140 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_TerminalOut), (176 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WaitKey), (12 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_Write), (56 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteDownBuffer), (56 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteDownBufferNoLock), (92 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteNoLock), (92 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteSkipNoLock), (112 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteString), (26 bytes).
    Removing segger_rtt.o(i.SEGGER_RTT_WriteWithOverwriteNoLock), (148 bytes).
    Removing segger_rtt.o(i._DoInit), (92 bytes).
    Removing segger_rtt.o(i._GetAvailWriteSpace), (22 bytes).
    Removing segger_rtt.o(i._PostTerminalSwitch), (32 bytes).
    Removing segger_rtt.o(i._WriteBlocking), (90 bytes).
    Removing segger_rtt.o(i._WriteNoCheck), (66 bytes).
    Removing segger_rtt.o(.bss), (1208 bytes).
    Removing segger_rtt.o(.constdata), (17 bytes).
    Removing segger_rtt.o(.data), (17 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_printf), (22 bytes).
    Removing segger_rtt_printf.o(i.SEGGER_RTT_vprintf), (382 bytes).
    Removing segger_rtt_printf.o(i._PrintInt), (196 bytes).
    Removing segger_rtt_printf.o(i._PrintUnsigned), (204 bytes).
    Removing segger_rtt_printf.o(i._StoreChar), (62 bytes).
    Removing segger_rtt_printf.o(.constdata), (16 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing drem.o(.text), (134 bytes).
    Removing dfltul.o(.text), (24 bytes).
    Removing dsqrt.o(.text), (162 bytes).

1225 unused section(s) (total 71447 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcat.c         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncat.c        0x00000000   Number         0  strncat.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/time/localtime.c        0x00000000   Number         0  localtime_w.o ABSOLUTE
    ../clib/microlib/time/localtime.c        0x00000000   Number         0  localtime.o ABSOLUTE
    ../clib/microlib/time/localtime.c        0x00000000   Number         0  localtime_i.o ABSOLUTE
    ../clib/microlib/time/mktime.c           0x00000000   Number         0  mktime.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprem.c                0x00000000   Number         0  drem.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp_x.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod_x.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\Common\src\data_convert.c             0x00000000   Number         0  data_convert.o ABSOLUTE
    ..\Library\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\Library\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\RTT\SEGGER_RTT.c                      0x00000000   Number         0  segger_rtt.o ABSOLUTE
    ..\RTT\SEGGER_RTT_printf.c               0x00000000   Number         0  segger_rtt_printf.o ABSOLUTE
    ..\Source\src\INS_Data.c                 0x00000000   Number         0  ins_data.o ABSOLUTE
    ..\Source\src\INS_Sys.c                  0x00000000   Number         0  ins_sys.o ABSOLUTE
    ..\Source\src\TCPServer.c                0x00000000   Number         0  tcpserver.o ABSOLUTE
    ..\Source\src\Time_Unify.c               0x00000000   Number         0  time_unify.o ABSOLUTE
    ..\Source\src\can_data.c                 0x00000000   Number         0  can_data.o ABSOLUTE
    ..\Source\src\gd32f4xx_it.c              0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\Source\src\gnss.c                     0x00000000   Number         0  gnss.o ABSOLUTE
    ..\Source\src\imu_data.c                 0x00000000   Number         0  imu_data.o ABSOLUTE
    ..\Source\src\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    ..\Source\src\systick.c                  0x00000000   Number         0  systick.o ABSOLUTE
    ..\\Library\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\Source\\src\\INS_Data.c              0x00000000   Number         0  ins_data.o ABSOLUTE
    ..\\Source\\src\\INS_Sys.c               0x00000000   Number         0  ins_sys.o ABSOLUTE
    ..\\Source\\src\\TCPServer.c             0x00000000   Number         0  tcpserver.o ABSOLUTE
    ..\\Source\\src\\Time_Unify.c            0x00000000   Number         0  time_unify.o ABSOLUTE
    ..\\Source\\src\\can_data.c              0x00000000   Number         0  can_data.o ABSOLUTE
    ..\\Source\\src\\gd32f4xx_it.c           0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\Source\\src\\gnss.c                  0x00000000   Number         0  gnss.o ABSOLUTE
    ..\\Source\\src\\imu_data.c              0x00000000   Number         0  imu_data.o ABSOLUTE
    ..\\Source\\src\\main.c                  0x00000000   Number         0  main.o ABSOLUTE
    ..\\Source\\src\\systick.c               0x00000000   Number         0  systick.o ABSOLUTE
    ..\\bsp\\src\\CH378_HAL.C                0x00000000   Number         0  ch378_hal.o ABSOLUTE
    ..\\bsp\\src\\CH378_SPI_HW.C             0x00000000   Number         0  ch378_spi_hw.o ABSOLUTE
    ..\\bsp\\src\\CH395CMD.C                 0x00000000   Number         0  ch395cmd.o ABSOLUTE
    ..\\bsp\\src\\CH395SPI.C                 0x00000000   Number         0  ch395spi.o ABSOLUTE
    ..\\bsp\\src\\FILE_SYS.C                 0x00000000   Number         0  file_sys.o ABSOLUTE
    ..\\bsp\\src\\Logger.c                   0x00000000   Number         0  logger.o ABSOLUTE
    ..\\bsp\\src\\bmp280.c                   0x00000000   Number         0  bmp280.o ABSOLUTE
    ..\\bsp\\src\\bsp_adc.c                  0x00000000   Number         0  bsp_adc.o ABSOLUTE
    ..\\bsp\\src\\bsp_can.c                  0x00000000   Number         0  bsp_can.o ABSOLUTE
    ..\\bsp\\src\\bsp_exti.c                 0x00000000   Number         0  bsp_exti.o ABSOLUTE
    ..\\bsp\\src\\bsp_flash.c                0x00000000   Number         0  bsp_flash.o ABSOLUTE
    ..\\bsp\\src\\bsp_fmc.c                  0x00000000   Number         0  bsp_fmc.o ABSOLUTE
    ..\\bsp\\src\\bsp_fwdgt.c                0x00000000   Number         0  bsp_fwdgt.o ABSOLUTE
    ..\\bsp\\src\\bsp_gpio.c                 0x00000000   Number         0  bsp_gpio.o ABSOLUTE
    ..\\bsp\\src\\bsp_i2c.c                  0x00000000   Number         0  bsp_i2c.o ABSOLUTE
    ..\\bsp\\src\\bsp_rtc.c                  0x00000000   Number         0  bsp_rtc.o ABSOLUTE
    ..\\bsp\\src\\bsp_sys.c                  0x00000000   Number         0  bsp_sys.o ABSOLUTE
    ..\\bsp\\src\\bsp_tim.c                  0x00000000   Number         0  bsp_tim.o ABSOLUTE
    ..\\bsp\\src\\bsp_uart.c                 0x00000000   Number         0  bsp_uart.o ABSOLUTE
    ..\\bsp\\src\\common.c                   0x00000000   Number         0  common.o ABSOLUTE
    ..\bsp\src\CH378_HAL.C                   0x00000000   Number         0  ch378_hal.o ABSOLUTE
    ..\bsp\src\CH378_SPI_HW.C                0x00000000   Number         0  ch378_spi_hw.o ABSOLUTE
    ..\bsp\src\CH395CMD.C                    0x00000000   Number         0  ch395cmd.o ABSOLUTE
    ..\bsp\src\CH395SPI.C                    0x00000000   Number         0  ch395spi.o ABSOLUTE
    ..\bsp\src\FILE_SYS.C                    0x00000000   Number         0  file_sys.o ABSOLUTE
    ..\bsp\src\Logger.c                      0x00000000   Number         0  logger.o ABSOLUTE
    ..\bsp\src\TCP_Server.c                  0x00000000   Number         0  tcp_server.o ABSOLUTE
    ..\bsp\src\bmp2.c                        0x00000000   Number         0  bmp2.o ABSOLUTE
    ..\bsp\src\bmp280.c                      0x00000000   Number         0  bmp280.o ABSOLUTE
    ..\bsp\src\bsp_adc.c                     0x00000000   Number         0  bsp_adc.o ABSOLUTE
    ..\bsp\src\bsp_can.c                     0x00000000   Number         0  bsp_can.o ABSOLUTE
    ..\bsp\src\bsp_exti.c                    0x00000000   Number         0  bsp_exti.o ABSOLUTE
    ..\bsp\src\bsp_flash.c                   0x00000000   Number         0  bsp_flash.o ABSOLUTE
    ..\bsp\src\bsp_fmc.c                     0x00000000   Number         0  bsp_fmc.o ABSOLUTE
    ..\bsp\src\bsp_fwdgt.c                   0x00000000   Number         0  bsp_fwdgt.o ABSOLUTE
    ..\bsp\src\bsp_gpio.c                    0x00000000   Number         0  bsp_gpio.o ABSOLUTE
    ..\bsp\src\bsp_i2c.c                     0x00000000   Number         0  bsp_i2c.o ABSOLUTE
    ..\bsp\src\bsp_rtc.c                     0x00000000   Number         0  bsp_rtc.o ABSOLUTE
    ..\bsp\src\bsp_sys.c                     0x00000000   Number         0  bsp_sys.o ABSOLUTE
    ..\bsp\src\bsp_tim.c                     0x00000000   Number         0  bsp_tim.o ABSOLUTE
    ..\bsp\src\bsp_uart.c                    0x00000000   Number         0  bsp_uart.o ABSOLUTE
    ..\bsp\src\common.c                      0x00000000   Number         0  common.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c0   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x080001c0   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080001e4   Section        0  mktime.o(.text)
    .text                                    0x0800028c   Section        0  memcpya.o(.text)
    .text                                    0x080002b0   Section        0  memseta.o(.text)
    .text                                    0x080002d4   Section        0  strlen.o(.text)
    .text                                    0x080002e2   Section        0  strcpy.o(.text)
    .text                                    0x080002f4   Section        0  dadd.o(.text)
    .text                                    0x08000442   Section        0  dmul.o(.text)
    .text                                    0x08000526   Section        0  ddiv.o(.text)
    .text                                    0x08000604   Section        0  dflti.o(.text)
    .text                                    0x08000626   Section        0  dfltui.o(.text)
    .text                                    0x08000640   Section        0  dfixi.o(.text)
    .text                                    0x0800067e   Section        0  f2d.o(.text)
    .text                                    0x080006a4   Section       48  cdcmple.o(.text)
    .text                                    0x080006d4   Section       48  cdrcmple.o(.text)
    .text                                    0x08000704   Section        0  uidiv.o(.text)
    .text                                    0x08000730   Section        0  uldiv.o(.text)
    .text                                    0x08000792   Section        0  llshl.o(.text)
    .text                                    0x080007b0   Section        0  llushr.o(.text)
    .text                                    0x080007d0   Section        0  llsshr.o(.text)
    .text                                    0x080007f4   Section        0  localtime_w.o(.text)
    .text                                    0x08000800   Section        0  localtime_i.o(.text)
    .text                                    0x0800088c   Section        0  iusefp.o(.text)
    .text                                    0x0800088c   Section        0  depilogue.o(.text)
    .text                                    0x08000946   Section        0  dfixul.o(.text)
    .text                                    0x08000978   Section       36  init.o(.text)
    .text                                    0x0800099c   Section        0  __dczerorl2.o(.text)
    i.ADC_IRQHandler                         0x080009f4   Section        0  gd32f4xx_it.o(i.ADC_IRQHandler)
    i.BusFault_Handler                       0x08000a00   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.CAN0_RX1_IRQHandler                    0x08000a04   Section        0  bsp_can.o(i.CAN0_RX1_IRQHandler)
    i.CAN1_RX1_IRQHandler                    0x08000b90   Section        0  bsp_can.o(i.CAN1_RX1_IRQHandler)
    i.CH378ByteLocate                        0x08000bc4   Section        0  file_sys.o(i.CH378ByteLocate)
    i.CH378ByteWrite                         0x08000c00   Section        0  file_sys.o(i.CH378ByteWrite)
    i.CH378FileCreate                        0x08000c48   Section        0  file_sys.o(i.CH378FileCreate)
    i.CH378FileOpen                          0x08000c58   Section        0  file_sys.o(i.CH378FileOpen)
    i.CH378GetIntStatus                      0x08000c68   Section        0  file_sys.o(i.CH378GetIntStatus)
    i.CH378SendCmdWaitInt                    0x08000c88   Section        0  file_sys.o(i.CH378SendCmdWaitInt)
    i.CH378SetFileName                       0x08000ca4   Section        0  file_sys.o(i.CH378SetFileName)
    i.CH378WriteOfsBlock                     0x08000cdc   Section        0  file_sys.o(i.CH378WriteOfsBlock)
    i.CH378_Port_Init                        0x08000d30   Section        0  ch378_spi_hw.o(i.CH378_Port_Init)
    i.CH378_mDelayuS                         0x08000de0   Section        0  ch378_hal.o(i.CH378_mDelayuS)
    i.CH395CMDInitCH395                      0x08000de4   Section        0  ch395cmd.o(i.CH395CMDInitCH395)
    i.CH395GetCmdStatus                      0x08000e0a   Section        0  ch395cmd.o(i.CH395GetCmdStatus)
    i.CH395OpenSocket                        0x08000e1a   Section        0  ch395cmd.o(i.CH395OpenSocket)
    i.CH395SetSocketProtType                 0x08000e48   Section        0  ch395cmd.o(i.CH395SetSocketProtType)
    i.CH395SetSocketRecvBuf                  0x08000e64   Section        0  ch395cmd.o(i.CH395SetSocketRecvBuf)
    i.CH395SetSocketSendBuf                  0x08000e88   Section        0  ch395cmd.o(i.CH395SetSocketSendBuf)
    i.CH395SetSocketSourPort                 0x08000eac   Section        0  ch395cmd.o(i.CH395SetSocketSourPort)
    i.CH395SetStartPara                      0x08000ece   Section        0  ch395cmd.o(i.CH395SetStartPara)
    i.CH395TCPListen                         0x08000ef8   Section        0  ch395cmd.o(i.CH395TCPListen)
    i.CH395_PORT_INIT                        0x08000f28   Section        0  ch395spi.o(i.CH395_PORT_INIT)
    i.CH395_RST                              0x08001008   Section        0  ch395spi.o(i.CH395_RST)
    i.DRam_Read                              0x08001030   Section        0  bsp_fmc.o(i.DRam_Read)
    i.DRam_Write                             0x08001058   Section        0  bsp_fmc.o(i.DRam_Write)
    i.DebugMon_Handler                       0x08001080   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.EXTI10_15_IRQHandler                   0x08001082   Section        0  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    i.EXTI5_9_IRQHandler                     0x080010a0   Section        0  gd32f4xx_it.o(i.EXTI5_9_IRQHandler)
    i.GetChipID                              0x08001144   Section        0  main.o(i.GetChipID)
    i.HardFault_Handler                      0x08001170   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.INS_Init                               0x08001174   Section        0  main.o(i.INS_Init)
    i.InitFlashAddr                          0x08001200   Section        0  bsp_flash.o(i.InitFlashAddr)
    i.LEDIndicator                           0x08001214   Section        0  main.o(i.LEDIndicator)
    i.MemManage_Handler                      0x08001268   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800126a   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.Query378Interrupt                      0x0800126c   Section        0  ch378_spi_hw.o(i.Query378Interrupt)
    i.ReadFlash                              0x08001284   Section        0  bsp_flash.o(i.ReadFlash)
    i.ReadFlashByAddr                        0x080012a8   Section        0  bsp_flash.o(i.ReadFlashByAddr)
    i.SPI_Exchange                           0x080012c8   Section        0  ch378_spi_hw.o(i.SPI_Exchange)
    i.SetDefaultProductInfo                  0x080012fc   Section        0  main.o(i.SetDefaultProductInfo)
    i.Spi395Exchange                         0x08001360   Section        0  ch395spi.o(i.Spi395Exchange)
    i.SysTick_Handler                        0x08001394   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001398   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TAMPER_STAMP_IRQHandler                0x08001420   Section        0  bsp_rtc.o(i.TAMPER_STAMP_IRQHandler)
    i.TCPServer_Init                         0x08001448   Section        0  tcpserver.o(i.TCPServer_Init)
    i.TIMER0_UP_TIMER9_IRQHandler            0x08001498   Section        0  gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler)
    i.TIMER1_IRQHandler                      0x080014f0   Section        0  gd32f4xx_it.o(i.TIMER1_IRQHandler)
    i.UsageFault_Handler                     0x08001508   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.Wait378Interrupt                       0x0800150c   Section        0  file_sys.o(i.Wait378Interrupt)
    i.WriteFlash                             0x08001534   Section        0  bsp_flash.o(i.WriteFlash)
    i.WriteOld                               0x08001604   Section        0  bsp_flash.o(i.WriteOld)
    i.__0printf                              0x08001640   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08001660   Section        0  printfa.o(i.__0sprintf)
    i.__hardfp_floor                         0x08001688   Section        0  floor.o(i.__hardfp_floor)
    i.__scatterload_copy                     0x080017a0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080017ae   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080017b0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080017c0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080017c1   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08001944   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08001945   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08002020   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08002021   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002044   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002045   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08002072   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08002073   Thumb Code    10  printfa.o(i._sputc)
    i.adc_interrupt_flag_clear               0x0800207c   Section        0  gd32f4xx_adc.o(i.adc_interrupt_flag_clear)
    i.bsp_gpio_init                          0x08002084   Section        0  bsp_gpio.o(i.bsp_gpio_init)
    i.bsp_rtc_init                           0x08002254   Section        0  bsp_rtc.o(i.bsp_rtc_init)
    i.bsp_tim_init                           0x080022b4   Section        0  bsp_tim.o(i.bsp_tim_init)
    i.can_error_get                          0x08002354   Section        0  gd32f4xx_can.o(i.can_error_get)
    i.can_interrupt_flag_get                 0x0800235c   Section        0  gd32f4xx_can.o(i.can_interrupt_flag_get)
    i.can_message_receive                    0x080023b8   Section        0  gd32f4xx_can.o(i.can_message_receive)
    i.can_receive_message_length_get         0x08002446   Section        0  gd32f4xx_can.o(i.can_receive_message_length_get)
    i.ch395_socket_tcp_server_init           0x0800245c   Section        0  tcpserver.o(i.ch395_socket_tcp_server_init)
    i.delay_decrement                        0x080024f4   Section        0  systick.o(i.delay_decrement)
    i.delay_init                             0x08002508   Section        0  systick.o(i.delay_init)
    i.delay_ms                               0x0800253c   Section        0  systick.o(i.delay_ms)
    i.delay_us                               0x0800254c   Section        0  systick.o(i.delay_us)
    i.epoch2time                             0x08002580   Section        0  time_unify.o(i.epoch2time)
    i.exmc_asynchronous_sram_init            0x08002684   Section        0  bsp_fmc.o(i.exmc_asynchronous_sram_init)
    i.exmc_norsram_enable                    0x080027e4   Section        0  gd32f4xx_exmc.o(i.exmc_norsram_enable)
    i.exmc_norsram_init                      0x080027f4   Section        0  gd32f4xx_exmc.o(i.exmc_norsram_init)
    i.exti_interrupt_flag_clear              0x080028b0   Section        0  gd32f4xx_exti.o(i.exti_interrupt_flag_clear)
    i.exti_interrupt_flag_get                0x080028bc   Section        0  gd32f4xx_exti.o(i.exti_interrupt_flag_get)
    i.fmc_flag_clear                         0x080028e0   Section        0  gd32f4xx_fmc.o(i.fmc_flag_clear)
    i.fmc_halfword_program                   0x080028ec   Section        0  gd32f4xx_fmc.o(i.fmc_halfword_program)
    i.fmc_ready_wait                         0x08002930   Section        0  gd32f4xx_fmc.o(i.fmc_ready_wait)
    i.fmc_sector_erase                       0x0800294c   Section        0  gd32f4xx_fmc.o(i.fmc_sector_erase)
    i.fmc_state_get                          0x08002998   Section        0  gd32f4xx_fmc.o(i.fmc_state_get)
    i.fmc_unlock                             0x080029d4   Section        0  gd32f4xx_fmc.o(i.fmc_unlock)
    i.fputc                                  0x080029f8   Section        0  main.o(i.fputc)
    i.generateCSVLogFileName                 0x08002a1c   Section        0  logger.o(i.generateCSVLogFileName)
    i.getRTCWeekSecond                       0x08002a94   Section        0  bsp_rtc.o(i.getRTCWeekSecond)
    i.gpio_af_set                            0x08002be8   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08002c3e   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08002c42   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_input_bit_get                     0x08002c46   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08002c50   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08002c98   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.gpst2time                              0x08002cd8   Section        0  time_unify.o(i.gpst2time)
    i.gpst2utc                               0x08002d70   Section        0  time_unify.o(i.gpst2utc)
    i.isLeapYear                             0x08002e0c   Section        0  bsp_rtc.o(i.isLeapYear)
    i.mDelaymS                               0x08002e32   Section        0  ch395spi.o(i.mDelaymS)
    i.mInitCH378Host                         0x08002e38   Section        0  ch378_spi_hw.o(i.mInitCH378Host)
    i.main                                   0x08002e94   Section        0  main.o(i.main)
    i.pmu_backup_write_enable                0x08002f44   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.rcu_all_reset_flag_clear               0x08002f54   Section        0  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    i.rcu_flag_get                           0x08002f64   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x08002f88   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x08002fa8   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x08003094   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x080030b4   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x080030d4   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x080030f4   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.rcu_timer_clock_prescaler_config       0x08003108   Section        0  gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config)
    i.rtc_current_time_get                   0x08003124   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_flag_clear                         0x08003180   Section        0  gd32f4xx_rtc.o(i.rtc_flag_clear)
    i.rtc_flag_get                           0x08003190   Section        0  gd32f4xx_rtc.o(i.rtc_flag_get)
    i.rtc_init                               0x080031a4   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08003240   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x0800327c   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_interrupt_enable                   0x0800328c   Section        0  gd32f4xx_rtc.o(i.rtc_interrupt_enable)
    i.rtc_pre_config                         0x080032c0   Section        0  bsp_rtc.o(i.rtc_pre_config)
    i.rtc_register_sync_wait                 0x080032ec   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.rtc_setup                              0x0800333c   Section        0  bsp_rtc.o(i.rtc_setup)
    i.rtc_show_timestamp                     0x08003348   Section        0  bsp_rtc.o(i.rtc_show_timestamp)
    i.rtc_subsecond_get                      0x080034a0   Section        0  gd32f4xx_rtc.o(i.rtc_subsecond_get)
    i.rtc_timestamp_enable                   0x080034b0   Section        0  gd32f4xx_rtc.o(i.rtc_timestamp_enable)
    i.rtc_timestamp_get                      0x080034d8   Section        0  gd32f4xx_rtc.o(i.rtc_timestamp_get)
    i.rtc_timestamp_subsecond_get            0x08003514   Section        0  gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get)
    i.socket_buffer_config                   0x08003520   Section        0  tcpserver.o(i.socket_buffer_config)
    i.spi_enable                             0x08003576   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_i2s_data_receive                   0x08003580   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x08003586   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x0800358a   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x08003594   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.system_clock_200m_25m_hxtal            0x080035c4   Section        0  system_gd32f4xx.o(i.system_clock_200m_25m_hxtal)
    system_clock_200m_25m_hxtal              0x080035c5   Thumb Code   158  system_gd32f4xx.o(i.system_clock_200m_25m_hxtal)
    i.systick_clksource_set                  0x08003670   Section        0  gd32f4xx_misc.o(i.systick_clksource_set)
    i.time2gpst                              0x08003688   Section        0  time_unify.o(i.time2gpst)
    i.timeSync                               0x080036d8   Section        0  bsp_rtc.o(i.timeSync)
    i.timeadd                                0x080037b8   Section        0  time_unify.o(i.timeadd)
    i.timediff                               0x08003822   Section        0  time_unify.o(i.timediff)
    i.timer_deinit                           0x0800385c   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_enable                           0x08003928   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x08003934   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_interrupt_enable                 0x080039c8   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_get               0x080039d0   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.trng_configuration                     0x080039e4   Section        0  main.o(i.trng_configuration)
    i.trng_deinit                            0x080039fe   Section        0  gd32f4xx_trng.o(i.trng_deinit)
    i.trng_enable                            0x08003a14   Section        0  gd32f4xx_trng.o(i.trng_enable)
    i.trng_flag_get                          0x08003a24   Section        0  gd32f4xx_trng.o(i.trng_flag_get)
    i.trng_ready_check                       0x08003a38   Section        0  main.o(i.trng_ready_check)
    i.usart_data_transmit                    0x08003a62   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_flag_get                         0x08003a6a   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.writeCSVFileHead                       0x08003a80   Section        0  logger.o(i.writeCSVFileHead)
    i.writeCSVLog                            0x08003ae8   Section        0  logger.o(i.writeCSVLog)
    i.xReadCH378Data                         0x08003ba4   Section        0  ch378_spi_hw.o(i.xReadCH378Data)
    i.xReadCH395Data                         0x08003baa   Section        0  ch395spi.o(i.xReadCH395Data)
    i.xWriteCH378Cmd                         0x08003bb0   Section        0  ch378_spi_hw.o(i.xWriteCH378Cmd)
    i.xWriteCH378Data                        0x08003be8   Section        0  ch378_spi_hw.o(i.xWriteCH378Data)
    i.xWriteCH395Cmd                         0x08003bec   Section        0  ch395spi.o(i.xWriteCH395Cmd)
    i.xWriteCH395Data                        0x08003c20   Section        0  ch395spi.o(i.xWriteCH395Data)
    i.yearDay                                0x08003c24   Section        0  bsp_rtc.o(i.yearDay)
    .constdata                               0x08003c80   Section       96  time_unify.o(.constdata)
    .constdata                               0x08003ce0   Section       12  mktime.o(.constdata)
    monlen                                   0x08003ce0   Data          12  mktime.o(.constdata)
    .constdata                               0x08003cec   Section       12  localtime_i.o(.constdata)
    monlen                                   0x08003cec   Data          12  localtime_i.o(.constdata)
    .conststring                             0x08003cf8   Section      181  logger.o(.conststring)
    .data                                    0x20000000   Section       24  main.o(.data)
    .data                                    0x20000018   Section        8  main.o(.data)
    .data                                    0x20000020   Section        8  systick.o(.data)
    fac_us                                   0x20000020   Data           1  systick.o(.data)
    delay                                    0x20000024   Data           4  systick.o(.data)
    .data                                    0x20000028   Section        2  ins_data.o(.data)
    .data                                    0x2000002a   Section        2  ins_data.o(.data)
    .data                                    0x2000002c   Section        1  ins_data.o(.data)
    .data                                    0x20000030   Section        4  ins_data.o(.data)
    .data                                    0x20000038   Section        8  ins_data.o(.data)
    .data                                    0x20000040   Section        8  tcpserver.o(.data)
    .data                                    0x20000048   Section     1344  time_unify.o(.data)
    .data                                    0x20000588   Section      224  bsp_can.o(.data)
    .data                                    0x20000668   Section       12  bsp_flash.o(.data)
    .data                                    0x20000674   Section       40  bsp_rtc.o(.data)
    gRTC                                     0x20000674   Data          40  bsp_rtc.o(.data)
    .data                                    0x2000069c   Section        8  bsp_rtc.o(.data)
    .data                                    0x200006a4   Section        4  bsp_tim.o(.data)
    .data                                    0x200006a8   Section        4  bsp_tim.o(.data)
    .data                                    0x200006ac   Section        4  bsp_tim.o(.data)
    .data                                    0x200006b0   Section        1  bsp_tim.o(.data)
    .data                                    0x200006b4   Section        4  bsp_tim.o(.data)
    .data                                    0x200006b8   Section        4  stdout.o(.data)
    .bss                                     0x200006bc   Section      256  main.o(.bss)
    .bss                                     0x200007bc   Section      160  ins_data.o(.bss)
    .bss                                     0x2000085c   Section      100  ins_data.o(.bss)
    .bss                                     0x200008c0   Section       76  ins_data.o(.bss)
    .bss                                     0x2000090c   Section       76  ins_data.o(.bss)
    .bss                                     0x20000958   Section      100  ins_data.o(.bss)
    .bss                                     0x200009bc   Section     1024  bsp_flash.o(.bss)
    .bss                                     0x20000dbc   Section       25  bsp_rtc.o(.bss)
    .bss                                     0x20000dd5   Section      601  logger.o(.bss)
    .bss                                     0x20001030   Section       44  localtime_w.o(.bss)
    _tms                                     0x20001030   Data          44  localtime_w.o(.bss)
    STACK                                    0x20001060   Section     1024  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_int                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_gd32f450_470.o(.text)
    SVC_Handler                              0x080001d3   Thumb Code     2  startup_gd32f450_470.o(.text)
    PendSV_Handler                           0x080001d7   Thumb Code     2  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART0_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    mktime                                   0x080001e5   Thumb Code   150  mktime.o(.text)
    __aeabi_memcpy                           0x0800028d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800028d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800028d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080002b1   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080002b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080002b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080002bf   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080002bf   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080002bf   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080002c3   Thumb Code    18  memseta.o(.text)
    strlen                                   0x080002d5   Thumb Code    14  strlen.o(.text)
    strcpy                                   0x080002e3   Thumb Code    18  strcpy.o(.text)
    __aeabi_dadd                             0x080002f5   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000437   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800043d   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000443   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000527   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08000605   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08000627   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2iz                             0x08000641   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x0800067f   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x080006a5   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x080006a5   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x080006d5   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_uidiv                            0x08000705   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000705   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000731   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08000793   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000793   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080007b1   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080007b1   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080007d1   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080007d1   Thumb Code     0  llsshr.o(.text)
    localtime                                0x080007f5   Thumb Code     6  localtime_w.o(.text)
    _localtime                               0x08000801   Thumb Code   134  localtime_i.o(.text)
    __I$use$fp                               0x0800088d   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x0800088d   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080008ab   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x08000947   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000979   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000979   Thumb Code     0  init.o(.text)
    __decompress                             0x0800099d   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x0800099d   Thumb Code    86  __dczerorl2.o(.text)
    ADC_IRQHandler                           0x080009f5   Thumb Code     8  gd32f4xx_it.o(i.ADC_IRQHandler)
    BusFault_Handler                         0x08000a01   Thumb Code     2  gd32f4xx_it.o(i.BusFault_Handler)
    CAN0_RX1_IRQHandler                      0x08000a05   Thumb Code   366  bsp_can.o(i.CAN0_RX1_IRQHandler)
    CAN1_RX1_IRQHandler                      0x08000b91   Thumb Code    48  bsp_can.o(i.CAN1_RX1_IRQHandler)
    CH378ByteLocate                          0x08000bc5   Thumb Code    56  file_sys.o(i.CH378ByteLocate)
    CH378ByteWrite                           0x08000c01   Thumb Code    66  file_sys.o(i.CH378ByteWrite)
    CH378FileCreate                          0x08000c49   Thumb Code    16  file_sys.o(i.CH378FileCreate)
    CH378FileOpen                            0x08000c59   Thumb Code    16  file_sys.o(i.CH378FileOpen)
    CH378GetIntStatus                        0x08000c69   Thumb Code    28  file_sys.o(i.CH378GetIntStatus)
    CH378SendCmdWaitInt                      0x08000c89   Thumb Code    24  file_sys.o(i.CH378SendCmdWaitInt)
    CH378SetFileName                         0x08000ca5   Thumb Code    50  file_sys.o(i.CH378SetFileName)
    CH378WriteOfsBlock                       0x08000cdd   Thumb Code    78  file_sys.o(i.CH378WriteOfsBlock)
    CH378_Port_Init                          0x08000d31   Thumb Code   164  ch378_spi_hw.o(i.CH378_Port_Init)
    CH378_mDelayuS                           0x08000de1   Thumb Code     4  ch378_hal.o(i.CH378_mDelayuS)
    CH395CMDInitCH395                        0x08000de5   Thumb Code    38  ch395cmd.o(i.CH395CMDInitCH395)
    CH395GetCmdStatus                        0x08000e0b   Thumb Code    16  ch395cmd.o(i.CH395GetCmdStatus)
    CH395OpenSocket                          0x08000e1b   Thumb Code    46  ch395cmd.o(i.CH395OpenSocket)
    CH395SetSocketProtType                   0x08000e49   Thumb Code    28  ch395cmd.o(i.CH395SetSocketProtType)
    CH395SetSocketRecvBuf                    0x08000e65   Thumb Code    36  ch395cmd.o(i.CH395SetSocketRecvBuf)
    CH395SetSocketSendBuf                    0x08000e89   Thumb Code    36  ch395cmd.o(i.CH395SetSocketSendBuf)
    CH395SetSocketSourPort                   0x08000ead   Thumb Code    34  ch395cmd.o(i.CH395SetSocketSourPort)
    CH395SetStartPara                        0x08000ecf   Thumb Code    42  ch395cmd.o(i.CH395SetStartPara)
    CH395TCPListen                           0x08000ef9   Thumb Code    46  ch395cmd.o(i.CH395TCPListen)
    CH395_PORT_INIT                          0x08000f29   Thumb Code   206  ch395spi.o(i.CH395_PORT_INIT)
    CH395_RST                                0x08001009   Thumb Code    36  ch395spi.o(i.CH395_RST)
    DRam_Read                                0x08001031   Thumb Code    40  bsp_fmc.o(i.DRam_Read)
    DRam_Write                               0x08001059   Thumb Code    40  bsp_fmc.o(i.DRam_Write)
    DebugMon_Handler                         0x08001081   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    EXTI10_15_IRQHandler                     0x08001083   Thumb Code    28  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    EXTI5_9_IRQHandler                       0x080010a1   Thumb Code   140  gd32f4xx_it.o(i.EXTI5_9_IRQHandler)
    GetChipID                                0x08001145   Thumb Code    32  main.o(i.GetChipID)
    HardFault_Handler                        0x08001171   Thumb Code     2  gd32f4xx_it.o(i.HardFault_Handler)
    INS_Init                                 0x08001175   Thumb Code   122  main.o(i.INS_Init)
    InitFlashAddr                            0x08001201   Thumb Code    10  bsp_flash.o(i.InitFlashAddr)
    LEDIndicator                             0x08001215   Thumb Code    80  main.o(i.LEDIndicator)
    MemManage_Handler                        0x08001269   Thumb Code     2  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800126b   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    Query378Interrupt                        0x0800126d   Thumb Code    20  ch378_spi_hw.o(i.Query378Interrupt)
    ReadFlash                                0x08001285   Thumb Code    30  bsp_flash.o(i.ReadFlash)
    ReadFlashByAddr                          0x080012a9   Thumb Code    26  bsp_flash.o(i.ReadFlashByAddr)
    SPI_Exchange                             0x080012c9   Thumb Code    48  ch378_spi_hw.o(i.SPI_Exchange)
    SetDefaultProductInfo                    0x080012fd   Thumb Code    90  main.o(i.SetDefaultProductInfo)
    Spi395Exchange                           0x08001361   Thumb Code    48  ch395spi.o(i.Spi395Exchange)
    SysTick_Handler                          0x08001395   Thumb Code     4  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08001399   Thumb Code   120  system_gd32f4xx.o(i.SystemInit)
    TAMPER_STAMP_IRQHandler                  0x08001421   Thumb Code    36  bsp_rtc.o(i.TAMPER_STAMP_IRQHandler)
    TCPServer_Init                           0x08001449   Thumb Code    76  tcpserver.o(i.TCPServer_Init)
    TIMER0_UP_TIMER9_IRQHandler              0x08001499   Thumb Code    68  gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler)
    TIMER1_IRQHandler                        0x080014f1   Thumb Code    20  gd32f4xx_it.o(i.TIMER1_IRQHandler)
    UsageFault_Handler                       0x08001509   Thumb Code     2  gd32f4xx_it.o(i.UsageFault_Handler)
    Wait378Interrupt                         0x0800150d   Thumb Code    36  file_sys.o(i.Wait378Interrupt)
    WriteFlash                               0x08001535   Thumb Code   194  bsp_flash.o(i.WriteFlash)
    WriteOld                                 0x08001605   Thumb Code    50  bsp_flash.o(i.WriteOld)
    __0printf                                0x08001641   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08001641   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08001641   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08001641   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08001641   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08001661   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08001661   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08001661   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08001661   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08001661   Thumb Code     0  printfa.o(i.__0sprintf)
    __hardfp_floor                           0x08001689   Thumb Code   252  floor.o(i.__hardfp_floor)
    __scatterload_copy                       0x080017a1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080017af   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080017b1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    adc_interrupt_flag_clear                 0x0800207d   Thumb Code     8  gd32f4xx_adc.o(i.adc_interrupt_flag_clear)
    bsp_gpio_init                            0x08002085   Thumb Code   448  bsp_gpio.o(i.bsp_gpio_init)
    bsp_rtc_init                             0x08002255   Thumb Code    90  bsp_rtc.o(i.bsp_rtc_init)
    bsp_tim_init                             0x080022b5   Thumb Code   154  bsp_tim.o(i.bsp_tim_init)
    can_error_get                            0x08002355   Thumb Code     8  gd32f4xx_can.o(i.can_error_get)
    can_interrupt_flag_get                   0x0800235d   Thumb Code    92  gd32f4xx_can.o(i.can_interrupt_flag_get)
    can_message_receive                      0x080023b9   Thumb Code   142  gd32f4xx_can.o(i.can_message_receive)
    can_receive_message_length_get           0x08002447   Thumb Code    22  gd32f4xx_can.o(i.can_receive_message_length_get)
    ch395_socket_tcp_server_init             0x0800245d   Thumb Code   148  tcpserver.o(i.ch395_socket_tcp_server_init)
    delay_decrement                          0x080024f5   Thumb Code    16  systick.o(i.delay_decrement)
    delay_init                               0x08002509   Thumb Code    46  systick.o(i.delay_init)
    delay_ms                                 0x0800253d   Thumb Code    12  systick.o(i.delay_ms)
    delay_us                                 0x0800254d   Thumb Code    48  systick.o(i.delay_us)
    epoch2time                               0x08002581   Thumb Code   256  time_unify.o(i.epoch2time)
    exmc_asynchronous_sram_init              0x08002685   Thumb Code   334  bsp_fmc.o(i.exmc_asynchronous_sram_init)
    exmc_norsram_enable                      0x080027e5   Thumb Code    16  gd32f4xx_exmc.o(i.exmc_norsram_enable)
    exmc_norsram_init                        0x080027f5   Thumb Code   184  gd32f4xx_exmc.o(i.exmc_norsram_init)
    exti_interrupt_flag_clear                0x080028b1   Thumb Code     6  gd32f4xx_exti.o(i.exti_interrupt_flag_clear)
    exti_interrupt_flag_get                  0x080028bd   Thumb Code    26  gd32f4xx_exti.o(i.exti_interrupt_flag_get)
    fmc_flag_clear                           0x080028e1   Thumb Code     6  gd32f4xx_fmc.o(i.fmc_flag_clear)
    fmc_halfword_program                     0x080028ed   Thumb Code    64  gd32f4xx_fmc.o(i.fmc_halfword_program)
    fmc_ready_wait                           0x08002931   Thumb Code    28  gd32f4xx_fmc.o(i.fmc_ready_wait)
    fmc_sector_erase                         0x0800294d   Thumb Code    70  gd32f4xx_fmc.o(i.fmc_sector_erase)
    fmc_state_get                            0x08002999   Thumb Code    54  gd32f4xx_fmc.o(i.fmc_state_get)
    fmc_unlock                               0x080029d5   Thumb Code    22  gd32f4xx_fmc.o(i.fmc_unlock)
    fputc                                    0x080029f9   Thumb Code    30  main.o(i.fputc)
    generateCSVLogFileName                   0x08002a1d   Thumb Code    76  logger.o(i.generateCSVLogFileName)
    getRTCWeekSecond                         0x08002a95   Thumb Code   326  bsp_rtc.o(i.getRTCWeekSecond)
    gpio_af_set                              0x08002be9   Thumb Code    86  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08002c3f   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08002c43   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_input_bit_get                       0x08002c47   Thumb Code    10  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08002c51   Thumb Code    72  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08002c99   Thumb Code    62  gd32f4xx_gpio.o(i.gpio_output_options_set)
    gpst2time                                0x08002cd9   Thumb Code   124  time_unify.o(i.gpst2time)
    gpst2utc                                 0x08002d71   Thumb Code   142  time_unify.o(i.gpst2utc)
    isLeapYear                               0x08002e0d   Thumb Code    38  bsp_rtc.o(i.isLeapYear)
    mDelaymS                                 0x08002e33   Thumb Code     4  ch395spi.o(i.mDelaymS)
    mInitCH378Host                           0x08002e39   Thumb Code    86  ch378_spi_hw.o(i.mInitCH378Host)
    main                                     0x08002e95   Thumb Code   148  main.o(i.main)
    pmu_backup_write_enable                  0x08002f45   Thumb Code    12  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    rcu_all_reset_flag_clear                 0x08002f55   Thumb Code    12  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    rcu_flag_get                             0x08002f65   Thumb Code    30  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x08002f89   Thumb Code    26  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x08002fa9   Thumb Code   232  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x08003095   Thumb Code    26  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x080030b5   Thumb Code    26  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x080030d5   Thumb Code    26  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x080030f5   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rcu_timer_clock_prescaler_config         0x08003109   Thumb Code    22  gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config)
    rtc_current_time_get                     0x08003125   Thumb Code    88  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_flag_clear                           0x08003181   Thumb Code    10  gd32f4xx_rtc.o(i.rtc_flag_clear)
    rtc_flag_get                             0x08003191   Thumb Code    16  gd32f4xx_rtc.o(i.rtc_flag_get)
    rtc_init                                 0x080031a5   Thumb Code   150  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08003241   Thumb Code    54  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x0800327d   Thumb Code    12  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_interrupt_enable                     0x0800328d   Thumb Code    46  gd32f4xx_rtc.o(i.rtc_interrupt_enable)
    rtc_pre_config                           0x080032c1   Thumb Code    42  bsp_rtc.o(i.rtc_pre_config)
    rtc_register_sync_wait                   0x080032ed   Thumb Code    76  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    rtc_setup                                0x0800333d   Thumb Code    12  bsp_rtc.o(i.rtc_setup)
    rtc_show_timestamp                       0x08003349   Thumb Code   266  bsp_rtc.o(i.rtc_show_timestamp)
    rtc_subsecond_get                        0x080034a1   Thumb Code    12  gd32f4xx_rtc.o(i.rtc_subsecond_get)
    rtc_timestamp_enable                     0x080034b1   Thumb Code    36  gd32f4xx_rtc.o(i.rtc_timestamp_enable)
    rtc_timestamp_get                        0x080034d9   Thumb Code    54  gd32f4xx_rtc.o(i.rtc_timestamp_get)
    rtc_timestamp_subsecond_get              0x08003515   Thumb Code     6  gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get)
    socket_buffer_config                     0x08003521   Thumb Code    86  tcpserver.o(i.socket_buffer_config)
    spi_enable                               0x08003577   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_i2s_data_receive                     0x08003581   Thumb Code     6  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x08003587   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x0800358b   Thumb Code    10  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x08003595   Thumb Code    46  gd32f4xx_spi.o(i.spi_init)
    systick_clksource_set                    0x08003671   Thumb Code    24  gd32f4xx_misc.o(i.systick_clksource_set)
    time2gpst                                0x08003689   Thumb Code    70  time_unify.o(i.time2gpst)
    timeSync                                 0x080036d9   Thumb Code   224  bsp_rtc.o(i.timeSync)
    timeadd                                  0x080037b9   Thumb Code   106  time_unify.o(i.timeadd)
    timediff                                 0x08003823   Thumb Code    58  time_unify.o(i.timediff)
    timer_deinit                             0x0800385d   Thumb Code   190  gd32f4xx_timer.o(i.timer_deinit)
    timer_enable                             0x08003929   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x08003935   Thumb Code   120  gd32f4xx_timer.o(i.timer_init)
    timer_interrupt_enable                   0x080039c9   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_get                 0x080039d1   Thumb Code    20  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    trng_configuration                       0x080039e5   Thumb Code    26  main.o(i.trng_configuration)
    trng_deinit                              0x080039ff   Thumb Code    22  gd32f4xx_trng.o(i.trng_deinit)
    trng_enable                              0x08003a15   Thumb Code    12  gd32f4xx_trng.o(i.trng_enable)
    trng_flag_get                            0x08003a25   Thumb Code    16  gd32f4xx_trng.o(i.trng_flag_get)
    trng_ready_check                         0x08003a39   Thumb Code    42  main.o(i.trng_ready_check)
    usart_data_transmit                      0x08003a63   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_flag_get                           0x08003a6b   Thumb Code    22  gd32f4xx_usart.o(i.usart_flag_get)
    writeCSVFileHead                         0x08003a81   Thumb Code    94  logger.o(i.writeCSVFileHead)
    writeCSVLog                              0x08003ae9   Thumb Code   154  logger.o(i.writeCSVLog)
    xReadCH378Data                           0x08003ba5   Thumb Code     6  ch378_spi_hw.o(i.xReadCH378Data)
    xReadCH395Data                           0x08003bab   Thumb Code     6  ch395spi.o(i.xReadCH395Data)
    xWriteCH378Cmd                           0x08003bb1   Thumb Code    50  ch378_spi_hw.o(i.xWriteCH378Cmd)
    xWriteCH378Data                          0x08003be9   Thumb Code     4  ch378_spi_hw.o(i.xWriteCH378Data)
    xWriteCH395Cmd                           0x08003bed   Thumb Code    46  ch395spi.o(i.xWriteCH395Cmd)
    xWriteCH395Data                          0x08003c21   Thumb Code     4  ch395spi.o(i.xWriteCH395Data)
    yearDay                                  0x08003c25   Thumb Code    88  bsp_rtc.o(i.yearDay)
    Region$$Table$$Base                      0x08003db0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003dd0   Number         0  anon$$obj.o(Region$$Table)
    dram_flag                                0x20000000   Data           1  main.o(.data)
    g_usb_ready                              0x20000001   Data           1  main.o(.data)
    g_pLogBuf                                0x20000004   Data           4  main.o(.data)
    g_gpsWeek                                0x20000008   Data           4  main.o(.data)
    g_gpsSecond                              0x20000010   Data           8  main.o(.data)
    g_LogBuf                                 0x20000018   Data           8  main.o(.data)
    fpga_data_read_flag                      0x20000028   Data           2  ins_data.o(.data)
    fpga_setting_update_flag                 0x2000002a   Data           2  ins_data.o(.data)
    g_LEDIndicatorState                      0x2000002c   Data           1  ins_data.o(.data)
    g_week                                   0x20000030   Data           4  ins_data.o(.data)
    g_second                                 0x20000038   Data           8  ins_data.o(.data)
    SocketServerStatus                       0x20000040   Data           1  tcpserver.o(.data)
    SocketServerPort                         0x20000042   Data           2  tcpserver.o(.data)
    ch395_status                             0x20000044   Data           4  tcpserver.o(.data)
    gpst0                                    0x20000048   Data          48  time_unify.o(.data)
    gst0                                     0x20000078   Data          48  time_unify.o(.data)
    bdt0                                     0x200000a8   Data          48  time_unify.o(.data)
    leaps                                    0x200000d8   Data        1008  time_unify.o(.data)
    mday                                     0x200004c8   Data         192  time_unify.o(.data)
    hCAN0                                    0x20000588   Data         112  bsp_can.o(.data)
    hCAN1                                    0x200005f8   Data         112  bsp_can.o(.data)
    g_NeedWrite                              0x20000668   Data           1  bsp_flash.o(.data)
    g_StartWrite                             0x20000669   Data           1  bsp_flash.o(.data)
    g_Addr                                   0x2000066c   Data           4  bsp_flash.o(.data)
    g_AddrBase                               0x20000670   Data           4  bsp_flash.o(.data)
    pRTC                                     0x2000069c   Data           4  bsp_rtc.o(.data)
    pTimestamep                              0x200006a0   Data           4  bsp_rtc.o(.data)
    time_periodic_sec_cnt                    0x200006a4   Data           4  bsp_tim.o(.data)
    time_periodic_min_cnt                    0x200006a8   Data           4  bsp_tim.o(.data)
    time_periodic_hour_cnt                   0x200006ac   Data           4  bsp_tim.o(.data)
    time_sync_flag                           0x200006b0   Data           1  bsp_tim.o(.data)
    time_base_periodic_cnt                   0x200006b4   Data           4  bsp_tim.o(.data)
    __stdout                                 0x200006b8   Data           4  stdout.o(.data)
    g_logFileName                            0x200006bc   Data         256  main.o(.bss)
    hINSData                                 0x200007bc   Data         160  ins_data.o(.bss)
    hINSFPGAData                             0x2000085c   Data         100  ins_data.o(.bss)
    hSetting                                 0x200008c0   Data          76  ins_data.o(.bss)
    hDefaultSetting                          0x2000090c   Data          76  ins_data.o(.bss)
    hFPGASetting                             0x20000958   Data         100  ins_data.o(.bss)
    g_FlashBuf                               0x200009bc   Data        1024  bsp_flash.o(.bss)
    gTimeStamp                               0x20000dbc   Data          25  bsp_rtc.o(.bss)
    g_charBuf                                0x20000dd5   Data         512  logger.o(.bss)
    curDate                                  0x20000fd5   Data          25  logger.o(.bss)
    fileBuf                                  0x20000fee   Data          64  logger.o(.bss)
    __initial_sp                             0x20001460   Data           0  startup_gd32f450_470.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000448c, Max: 0x00200000, ABSOLUTE, COMPRESSED[0x00003ef4])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003dd0, Max: 0x00200000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         8103    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         8692  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         9084    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         9087    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         9089    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         9091    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         9092    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         9094    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         9096    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         9085    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO         8104    .text               startup_gd32f450_470.o
    0x080001e4   0x080001e4   0x000000a8   Code   RO         8699    .text               mc_w.l(mktime.o)
    0x0800028c   0x0800028c   0x00000024   Code   RO         8702    .text               mc_w.l(memcpya.o)
    0x080002b0   0x080002b0   0x00000024   Code   RO         8704    .text               mc_w.l(memseta.o)
    0x080002d4   0x080002d4   0x0000000e   Code   RO         8712    .text               mc_w.l(strlen.o)
    0x080002e2   0x080002e2   0x00000012   Code   RO         8716    .text               mc_w.l(strcpy.o)
    0x080002f4   0x080002f4   0x0000014e   Code   RO         9025    .text               mf_w.l(dadd.o)
    0x08000442   0x08000442   0x000000e4   Code   RO         9027    .text               mf_w.l(dmul.o)
    0x08000526   0x08000526   0x000000de   Code   RO         9029    .text               mf_w.l(ddiv.o)
    0x08000604   0x08000604   0x00000022   Code   RO         9033    .text               mf_w.l(dflti.o)
    0x08000626   0x08000626   0x0000001a   Code   RO         9035    .text               mf_w.l(dfltui.o)
    0x08000640   0x08000640   0x0000003e   Code   RO         9037    .text               mf_w.l(dfixi.o)
    0x0800067e   0x0800067e   0x00000026   Code   RO         9039    .text               mf_w.l(f2d.o)
    0x080006a4   0x080006a4   0x00000030   Code   RO         9041    .text               mf_w.l(cdcmple.o)
    0x080006d4   0x080006d4   0x00000030   Code   RO         9043    .text               mf_w.l(cdrcmple.o)
    0x08000704   0x08000704   0x0000002c   Code   RO         9099    .text               mc_w.l(uidiv.o)
    0x08000730   0x08000730   0x00000062   Code   RO         9101    .text               mc_w.l(uldiv.o)
    0x08000792   0x08000792   0x0000001e   Code   RO         9103    .text               mc_w.l(llshl.o)
    0x080007b0   0x080007b0   0x00000020   Code   RO         9105    .text               mc_w.l(llushr.o)
    0x080007d0   0x080007d0   0x00000024   Code   RO         9107    .text               mc_w.l(llsshr.o)
    0x080007f4   0x080007f4   0x0000000c   Code   RO         9109    .text               mc_w.l(localtime_w.o)
    0x08000800   0x08000800   0x0000008c   Code   RO         9112    .text               mc_w.l(localtime_i.o)
    0x0800088c   0x0800088c   0x00000000   Code   RO         9132    .text               mc_w.l(iusefp.o)
    0x0800088c   0x0800088c   0x000000ba   Code   RO         9135    .text               mf_w.l(depilogue.o)
    0x08000946   0x08000946   0x00000030   Code   RO         9143    .text               mf_w.l(dfixul.o)
    0x08000976   0x08000976   0x00000002   PAD
    0x08000978   0x08000978   0x00000024   Code   RO         9145    .text               mc_w.l(init.o)
    0x0800099c   0x0800099c   0x00000056   Code   RO         9196    .text               mc_w.l(__dczerorl2.o)
    0x080009f2   0x080009f2   0x00000002   PAD
    0x080009f4   0x080009f4   0x0000000c   Code   RO         5598    i.ADC_IRQHandler    gd32f4xx_it.o
    0x08000a00   0x08000a00   0x00000002   Code   RO         5599    i.BusFault_Handler  gd32f4xx_it.o
    0x08000a02   0x08000a02   0x00000002   PAD
    0x08000a04   0x08000a04   0x0000018c   Code   RO         6597    i.CAN0_RX1_IRQHandler  bsp_can.o
    0x08000b90   0x08000b90   0x00000034   Code   RO         6598    i.CAN1_RX1_IRQHandler  bsp_can.o
    0x08000bc4   0x08000bc4   0x0000003c   Code   RO         7615    i.CH378ByteLocate   file_sys.o
    0x08000c00   0x08000c00   0x00000048   Code   RO         7618    i.CH378ByteWrite    file_sys.o
    0x08000c48   0x08000c48   0x00000010   Code   RO         7634    i.CH378FileCreate   file_sys.o
    0x08000c58   0x08000c58   0x00000010   Code   RO         7637    i.CH378FileOpen     file_sys.o
    0x08000c68   0x08000c68   0x00000020   Code   RO         7646    i.CH378GetIntStatus  file_sys.o
    0x08000c88   0x08000c88   0x0000001c   Code   RO         7660    i.CH378SendCmdWaitInt  file_sys.o
    0x08000ca4   0x08000ca4   0x00000038   Code   RO         7661    i.CH378SetFileName  file_sys.o
    0x08000cdc   0x08000cdc   0x00000054   Code   RO         7664    i.CH378WriteOfsBlock  file_sys.o
    0x08000d30   0x08000d30   0x000000b0   Code   RO         8049    i.CH378_Port_Init   ch378_spi_hw.o
    0x08000de0   0x08000de0   0x00000004   Code   RO         7133    i.CH378_mDelayuS    ch378_hal.o
    0x08000de4   0x08000de4   0x00000026   Code   RO         7185    i.CH395CMDInitCH395  ch395cmd.o
    0x08000e0a   0x08000e0a   0x00000010   Code   RO         7205    i.CH395GetCmdStatus  ch395cmd.o
    0x08000e1a   0x08000e1a   0x0000002e   Code   RO         7211    i.CH395OpenSocket   ch395cmd.o
    0x08000e48   0x08000e48   0x0000001c   Code   RO         7217    i.CH395SetSocketProtType  ch395cmd.o
    0x08000e64   0x08000e64   0x00000024   Code   RO         7218    i.CH395SetSocketRecvBuf  ch395cmd.o
    0x08000e88   0x08000e88   0x00000024   Code   RO         7219    i.CH395SetSocketSendBuf  ch395cmd.o
    0x08000eac   0x08000eac   0x00000022   Code   RO         7220    i.CH395SetSocketSourPort  ch395cmd.o
    0x08000ece   0x08000ece   0x0000002a   Code   RO         7221    i.CH395SetStartPara  ch395cmd.o
    0x08000ef8   0x08000ef8   0x0000002e   Code   RO         7225    i.CH395TCPListen    ch395cmd.o
    0x08000f26   0x08000f26   0x00000002   PAD
    0x08000f28   0x08000f28   0x000000e0   Code   RO         7504    i.CH395_PORT_INIT   ch395spi.o
    0x08001008   0x08001008   0x00000028   Code   RO         7505    i.CH395_RST         ch395spi.o
    0x08001030   0x08001030   0x00000028   Code   RO         6742    i.DRam_Read         bsp_fmc.o
    0x08001058   0x08001058   0x00000028   Code   RO         6743    i.DRam_Write        bsp_fmc.o
    0x08001080   0x08001080   0x00000002   Code   RO         5600    i.DebugMon_Handler  gd32f4xx_it.o
    0x08001082   0x08001082   0x0000001c   Code   RO         5601    i.EXTI10_15_IRQHandler  gd32f4xx_it.o
    0x0800109e   0x0800109e   0x00000002   PAD
    0x080010a0   0x080010a0   0x000000a4   Code   RO         5602    i.EXTI5_9_IRQHandler  gd32f4xx_it.o
    0x08001144   0x08001144   0x0000002c   Code   RO         5740    i.GetChipID         main.o
    0x08001170   0x08001170   0x00000002   Code   RO         5603    i.HardFault_Handler  gd32f4xx_it.o
    0x08001172   0x08001172   0x00000002   PAD
    0x08001174   0x08001174   0x0000008c   Code   RO         5741    i.INS_Init          main.o
    0x08001200   0x08001200   0x00000014   Code   RO         6689    i.InitFlashAddr     bsp_flash.o
    0x08001214   0x08001214   0x00000054   Code   RO         5742    i.LEDIndicator      main.o
    0x08001268   0x08001268   0x00000002   Code   RO         5604    i.MemManage_Handler  gd32f4xx_it.o
    0x0800126a   0x0800126a   0x00000002   Code   RO         5605    i.NMI_Handler       gd32f4xx_it.o
    0x0800126c   0x0800126c   0x00000018   Code   RO         8050    i.Query378Interrupt  ch378_spi_hw.o
    0x08001284   0x08001284   0x00000024   Code   RO         6690    i.ReadFlash         bsp_flash.o
    0x080012a8   0x080012a8   0x00000020   Code   RO         6691    i.ReadFlashByAddr   bsp_flash.o
    0x080012c8   0x080012c8   0x00000034   Code   RO         8051    i.SPI_Exchange      ch378_spi_hw.o
    0x080012fc   0x080012fc   0x00000064   Code   RO         5743    i.SetDefaultProductInfo  main.o
    0x08001360   0x08001360   0x00000034   Code   RO         7507    i.Spi395Exchange    ch395spi.o
    0x08001394   0x08001394   0x00000004   Code   RO         5606    i.SysTick_Handler   gd32f4xx_it.o
    0x08001398   0x08001398   0x00000088   Code   RO         8111    i.SystemInit        system_gd32f4xx.o
    0x08001420   0x08001420   0x00000028   Code   RO         6963    i.TAMPER_STAMP_IRQHandler  bsp_rtc.o
    0x08001448   0x08001448   0x00000050   Code   RO         6220    i.TCPServer_Init    tcpserver.o
    0x08001498   0x08001498   0x00000058   Code   RO         5607    i.TIMER0_UP_TIMER9_IRQHandler  gd32f4xx_it.o
    0x080014f0   0x080014f0   0x00000018   Code   RO         5608    i.TIMER1_IRQHandler  gd32f4xx_it.o
    0x08001508   0x08001508   0x00000002   Code   RO         5609    i.UsageFault_Handler  gd32f4xx_it.o
    0x0800150a   0x0800150a   0x00000002   PAD
    0x0800150c   0x0800150c   0x00000028   Code   RO         7667    i.Wait378Interrupt  file_sys.o
    0x08001534   0x08001534   0x000000d0   Code   RO         6692    i.WriteFlash        bsp_flash.o
    0x08001604   0x08001604   0x0000003c   Code   RO         6693    i.WriteOld          bsp_flash.o
    0x08001640   0x08001640   0x00000020   Code   RO         8961    i.__0printf         mc_w.l(printfa.o)
    0x08001660   0x08001660   0x00000028   Code   RO         8963    i.__0sprintf        mc_w.l(printfa.o)
    0x08001688   0x08001688   0x00000118   Code   RO         8660    i.__hardfp_floor    m_wm.l(floor.o)
    0x080017a0   0x080017a0   0x0000000e   Code   RO         9190    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080017ae   0x080017ae   0x00000002   Code   RO         9191    i.__scatterload_null  mc_w.l(handlers.o)
    0x080017b0   0x080017b0   0x0000000e   Code   RO         9192    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080017be   0x080017be   0x00000002   PAD
    0x080017c0   0x080017c0   0x00000184   Code   RO         8968    i._fp_digits        mc_w.l(printfa.o)
    0x08001944   0x08001944   0x000006dc   Code   RO         8969    i._printf_core      mc_w.l(printfa.o)
    0x08002020   0x08002020   0x00000024   Code   RO         8970    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002044   0x08002044   0x0000002e   Code   RO         8971    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08002072   0x08002072   0x0000000a   Code   RO         8973    i._sputc            mc_w.l(printfa.o)
    0x0800207c   0x0800207c   0x00000008   Code   RO           27    i.adc_interrupt_flag_clear  gd32f4xx_adc.o
    0x08002084   0x08002084   0x000001d0   Code   RO         6902    i.bsp_gpio_init     bsp_gpio.o
    0x08002254   0x08002254   0x00000060   Code   RO         6964    i.bsp_rtc_init      bsp_rtc.o
    0x080022b4   0x080022b4   0x000000a0   Code   RO         7079    i.bsp_tim_init      bsp_tim.o
    0x08002354   0x08002354   0x00000008   Code   RO          416    i.can_error_get     gd32f4xx_can.o
    0x0800235c   0x0800235c   0x0000005c   Code   RO          425    i.can_interrupt_flag_get  gd32f4xx_can.o
    0x080023b8   0x080023b8   0x0000008e   Code   RO          426    i.can_message_receive  gd32f4xx_can.o
    0x08002446   0x08002446   0x00000016   Code   RO          429    i.can_receive_message_length_get  gd32f4xx_can.o
    0x0800245c   0x0800245c   0x00000098   Code   RO         6223    i.ch395_socket_tcp_server_init  tcpserver.o
    0x080024f4   0x080024f4   0x00000014   Code   RO         5912    i.delay_decrement   systick.o
    0x08002508   0x08002508   0x00000034   Code   RO         5913    i.delay_init        systick.o
    0x0800253c   0x0800253c   0x00000010   Code   RO         5914    i.delay_ms          systick.o
    0x0800254c   0x0800254c   0x00000034   Code   RO         5915    i.delay_us          systick.o
    0x08002580   0x08002580   0x00000104   Code   RO         6278    i.epoch2time        time_unify.o
    0x08002684   0x08002684   0x00000160   Code   RO         6748    i.exmc_asynchronous_sram_init  bsp_fmc.o
    0x080027e4   0x080027e4   0x00000010   Code   RO         1938    i.exmc_norsram_enable  gd32f4xx_exmc.o
    0x080027f4   0x080027f4   0x000000bc   Code   RO         1939    i.exmc_norsram_init  gd32f4xx_exmc.o
    0x080028b0   0x080028b0   0x0000000c   Code   RO         2214    i.exti_interrupt_flag_clear  gd32f4xx_exti.o
    0x080028bc   0x080028bc   0x00000024   Code   RO         2215    i.exti_interrupt_flag_get  gd32f4xx_exti.o
    0x080028e0   0x080028e0   0x0000000c   Code   RO         2295    i.fmc_flag_clear    gd32f4xx_fmc.o
    0x080028ec   0x080028ec   0x00000044   Code   RO         2297    i.fmc_halfword_program  gd32f4xx_fmc.o
    0x08002930   0x08002930   0x0000001c   Code   RO         2305    i.fmc_ready_wait    gd32f4xx_fmc.o
    0x0800294c   0x0800294c   0x0000004c   Code   RO         2306    i.fmc_sector_erase  gd32f4xx_fmc.o
    0x08002998   0x08002998   0x0000003c   Code   RO         2307    i.fmc_state_get     gd32f4xx_fmc.o
    0x080029d4   0x080029d4   0x00000024   Code   RO         2308    i.fmc_unlock        gd32f4xx_fmc.o
    0x080029f8   0x080029f8   0x00000024   Code   RO         5747    i.fputc             main.o
    0x08002a1c   0x08002a1c   0x00000078   Code   RO         7972    i.generateCSVLogFileName  logger.o
    0x08002a94   0x08002a94   0x00000154   Code   RO         6965    i.getRTCWeekSecond  bsp_rtc.o
    0x08002be8   0x08002be8   0x00000056   Code   RO         2584    i.gpio_af_set       gd32f4xx_gpio.o
    0x08002c3e   0x08002c3e   0x00000004   Code   RO         2585    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08002c42   0x08002c42   0x00000004   Code   RO         2586    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08002c46   0x08002c46   0x0000000a   Code   RO         2590    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08002c50   0x08002c50   0x00000048   Code   RO         2592    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08002c98   0x08002c98   0x0000003e   Code   RO         2594    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08002cd6   0x08002cd6   0x00000002   PAD
    0x08002cd8   0x08002cd8   0x00000098   Code   RO         6280    i.gpst2time         time_unify.o
    0x08002d70   0x08002d70   0x0000009c   Code   RO         6281    i.gpst2utc          time_unify.o
    0x08002e0c   0x08002e0c   0x00000026   Code   RO         6966    i.isLeapYear        bsp_rtc.o
    0x08002e32   0x08002e32   0x00000004   Code   RO         7508    i.mDelaymS          ch395spi.o
    0x08002e36   0x08002e36   0x00000002   PAD
    0x08002e38   0x08002e38   0x0000005c   Code   RO         8052    i.mInitCH378Host    ch378_spi_hw.o
    0x08002e94   0x08002e94   0x000000b0   Code   RO         5749    i.main              main.o
    0x08002f44   0x08002f44   0x00000010   Code   RO         3214    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x08002f54   0x08002f54   0x00000010   Code   RO         3354    i.rcu_all_reset_flag_clear  gd32f4xx_rcu.o
    0x08002f64   0x08002f64   0x00000024   Code   RO         3365    i.rcu_flag_get      gd32f4xx_rcu.o
    0x08002f88   0x08002f88   0x00000020   Code   RO         3378    i.rcu_osci_on       gd32f4xx_rcu.o
    0x08002fa8   0x08002fa8   0x000000ec   Code   RO         3379    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x08003094   0x08003094   0x00000020   Code   RO         3381    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x080030b4   0x080030b4   0x00000020   Code   RO         3384    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x080030d4   0x080030d4   0x00000020   Code   RO         3385    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x080030f4   0x080030f4   0x00000014   Code   RO         3390    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08003108   0x08003108   0x0000001c   Code   RO         3397    i.rcu_timer_clock_prescaler_config  gd32f4xx_rcu.o
    0x08003124   0x08003124   0x0000005c   Code   RO         3662    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x08003180   0x08003180   0x00000010   Code   RO         3664    i.rtc_flag_clear    gd32f4xx_rtc.o
    0x08003190   0x08003190   0x00000014   Code   RO         3665    i.rtc_flag_get      gd32f4xx_rtc.o
    0x080031a4   0x080031a4   0x0000009c   Code   RO         3667    i.rtc_init          gd32f4xx_rtc.o
    0x08003240   0x08003240   0x0000003c   Code   RO         3668    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x0800327c   0x0800327c   0x00000010   Code   RO         3669    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x0800328c   0x0800328c   0x00000034   Code   RO         3671    i.rtc_interrupt_enable  gd32f4xx_rtc.o
    0x080032c0   0x080032c0   0x0000002a   Code   RO         6967    i.rtc_pre_config    bsp_rtc.o
    0x080032ea   0x080032ea   0x00000002   PAD
    0x080032ec   0x080032ec   0x00000050   Code   RO         3674    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x0800333c   0x0800333c   0x0000000c   Code   RO         6968    i.rtc_setup         bsp_rtc.o
    0x08003348   0x08003348   0x00000158   Code   RO         6969    i.rtc_show_timestamp  bsp_rtc.o
    0x080034a0   0x080034a0   0x00000010   Code   RO         3677    i.rtc_subsecond_get  gd32f4xx_rtc.o
    0x080034b0   0x080034b0   0x00000028   Code   RO         3682    i.rtc_timestamp_enable  gd32f4xx_rtc.o
    0x080034d8   0x080034d8   0x0000003c   Code   RO         3683    i.rtc_timestamp_get  gd32f4xx_rtc.o
    0x08003514   0x08003514   0x0000000c   Code   RO         3685    i.rtc_timestamp_subsecond_get  gd32f4xx_rtc.o
    0x08003520   0x08003520   0x00000056   Code   RO         6224    i.socket_buffer_config  tcpserver.o
    0x08003576   0x08003576   0x0000000a   Code   RO         4227    i.spi_enable        gd32f4xx_spi.o
    0x08003580   0x08003580   0x00000006   Code   RO         4229    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x08003586   0x08003586   0x00000004   Code   RO         4230    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x0800358a   0x0800358a   0x0000000a   Code   RO         4232    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x08003594   0x08003594   0x0000002e   Code   RO         4236    i.spi_init          gd32f4xx_spi.o
    0x080035c2   0x080035c2   0x00000002   PAD
    0x080035c4   0x080035c4   0x000000ac   Code   RO         8112    i.system_clock_200m_25m_hxtal  system_gd32f4xx.o
    0x08003670   0x08003670   0x00000018   Code   RO         3162    i.systick_clksource_set  gd32f4xx_misc.o
    0x08003688   0x08003688   0x00000050   Code   RO         6286    i.time2gpst         time_unify.o
    0x080036d8   0x080036d8   0x000000e0   Code   RO         6970    i.timeSync          bsp_rtc.o
    0x080037b8   0x080037b8   0x0000006a   Code   RO         6290    i.timeadd           time_unify.o
    0x08003822   0x08003822   0x0000003a   Code   RO         6291    i.timediff          time_unify.o
    0x0800385c   0x0800385c   0x000000cc   Code   RO         4547    i.timer_deinit      gd32f4xx_timer.o
    0x08003928   0x08003928   0x0000000a   Code   RO         4552    i.timer_enable      gd32f4xx_timer.o
    0x08003932   0x08003932   0x00000002   PAD
    0x08003934   0x08003934   0x00000094   Code   RO         4562    i.timer_init        gd32f4xx_timer.o
    0x080039c8   0x080039c8   0x00000008   Code   RO         4569    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x080039d0   0x080039d0   0x00000014   Code   RO         4571    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x080039e4   0x080039e4   0x0000001a   Code   RO         5750    i.trng_configuration  main.o
    0x080039fe   0x080039fe   0x00000016   Code   RO         5130    i.trng_deinit       gd32f4xx_trng.o
    0x08003a14   0x08003a14   0x00000010   Code   RO         5132    i.trng_enable       gd32f4xx_trng.o
    0x08003a24   0x08003a24   0x00000014   Code   RO         5133    i.trng_flag_get     gd32f4xx_trng.o
    0x08003a38   0x08003a38   0x0000002a   Code   RO         5751    i.trng_ready_check  main.o
    0x08003a62   0x08003a62   0x00000008   Code   RO         5204    i.usart_data_transmit  gd32f4xx_usart.o
    0x08003a6a   0x08003a6a   0x00000016   Code   RO         5211    i.usart_flag_get    gd32f4xx_usart.o
    0x08003a80   0x08003a80   0x00000068   Code   RO         7975    i.writeCSVFileHead  logger.o
    0x08003ae8   0x08003ae8   0x000000bc   Code   RO         7976    i.writeCSVLog       logger.o
    0x08003ba4   0x08003ba4   0x00000006   Code   RO         8053    i.xReadCH378Data    ch378_spi_hw.o
    0x08003baa   0x08003baa   0x00000006   Code   RO         7510    i.xReadCH395Data    ch395spi.o
    0x08003bb0   0x08003bb0   0x00000038   Code   RO         8054    i.xWriteCH378Cmd    ch378_spi_hw.o
    0x08003be8   0x08003be8   0x00000004   Code   RO         8055    i.xWriteCH378Data   ch378_spi_hw.o
    0x08003bec   0x08003bec   0x00000034   Code   RO         7511    i.xWriteCH395Cmd    ch395spi.o
    0x08003c20   0x08003c20   0x00000004   Code   RO         7512    i.xWriteCH395Data   ch395spi.o
    0x08003c24   0x08003c24   0x00000058   Code   RO         6971    i.yearDay           bsp_rtc.o
    0x08003c7c   0x08003c7c   0x00000004   PAD
    0x08003c80   0x08003c80   0x00000060   Data   RO         6294    .constdata          time_unify.o
    0x08003ce0   0x08003ce0   0x0000000c   Data   RO         8700    .constdata          mc_w.l(mktime.o)
    0x08003cec   0x08003cec   0x0000000c   Data   RO         9113    .constdata          mc_w.l(localtime_i.o)
    0x08003cf8   0x08003cf8   0x000000b5   Data   RO         7978    .conststring        logger.o
    0x08003dad   0x08003dad   0x00000003   PAD
    0x08003db0   0x08003db0   0x00000020   Data   RO         9188    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003dd0, Size: 0x00001460, Max: 0x00070000, ABSOLUTE, COMPRESSED[0x00000124])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000018   Data   RW         5753    .data               main.o
    0x20000018   COMPRESSED   0x00000008   Data   RW         5755    .data               main.o
    0x20000020   COMPRESSED   0x00000008   Data   RW         5918    .data               systick.o
    0x20000028   COMPRESSED   0x00000002   Data   RW         6165    .data               ins_data.o
    0x2000002a   COMPRESSED   0x00000002   Data   RW         6166    .data               ins_data.o
    0x2000002c   COMPRESSED   0x00000001   Data   RW         6167    .data               ins_data.o
    0x2000002d   COMPRESSED   0x00000003   PAD
    0x20000030   COMPRESSED   0x00000004   Data   RW         6168    .data               ins_data.o
    0x20000034   COMPRESSED   0x00000004   PAD
    0x20000038   COMPRESSED   0x00000008   Data   RW         6169    .data               ins_data.o
    0x20000040   COMPRESSED   0x00000008   Data   RW         6227    .data               tcpserver.o
    0x20000048   COMPRESSED   0x00000540   Data   RW         6295    .data               time_unify.o
    0x20000588   COMPRESSED   0x000000e0   Data   RW         6604    .data               bsp_can.o
    0x20000668   COMPRESSED   0x0000000c   Data   RW         6695    .data               bsp_flash.o
    0x20000674   COMPRESSED   0x00000028   Data   RW         6973    .data               bsp_rtc.o
    0x2000069c   COMPRESSED   0x00000008   Data   RW         6974    .data               bsp_rtc.o
    0x200006a4   COMPRESSED   0x00000004   Data   RW         7080    .data               bsp_tim.o
    0x200006a8   COMPRESSED   0x00000004   Data   RW         7081    .data               bsp_tim.o
    0x200006ac   COMPRESSED   0x00000004   Data   RW         7082    .data               bsp_tim.o
    0x200006b0   COMPRESSED   0x00000001   Data   RW         7083    .data               bsp_tim.o
    0x200006b1   COMPRESSED   0x00000003   PAD
    0x200006b4   COMPRESSED   0x00000004   Data   RW         7084    .data               bsp_tim.o
    0x200006b8   COMPRESSED   0x00000004   Data   RW         9098    .data               mc_w.l(stdout.o)
    0x200006bc        -       0x00000100   Zero   RW         5752    .bss                main.o
    0x200007bc        -       0x000000a0   Zero   RW         6160    .bss                ins_data.o
    0x2000085c        -       0x00000064   Zero   RW         6161    .bss                ins_data.o
    0x200008c0        -       0x0000004c   Zero   RW         6162    .bss                ins_data.o
    0x2000090c        -       0x0000004c   Zero   RW         6163    .bss                ins_data.o
    0x20000958        -       0x00000064   Zero   RW         6164    .bss                ins_data.o
    0x200009bc        -       0x00000400   Zero   RW         6694    .bss                bsp_flash.o
    0x20000dbc        -       0x00000019   Zero   RW         6972    .bss                bsp_rtc.o
    0x20000dd5        -       0x00000259   Zero   RW         7977    .bss                logger.o
    0x2000102e   COMPRESSED   0x00000002   PAD
    0x20001030        -       0x0000002c   Zero   RW         9110    .bss                mc_w.l(localtime_w.o)
    0x2000105c   COMPRESSED   0x00000004   PAD
    0x20001060        -       0x00000400   Zero   RW         8101    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       448         34          0        224          0       2534   bsp_can.o
       356         46          0         12       1024       4194   bsp_flash.o
       432         18          0          0          0       3243   bsp_fmc.o
       464         16          0          0          0        684   bsp_gpio.o
      1224        136          0         48         25       7678   bsp_rtc.o
       160          6          0         17          0       1130   bsp_tim.o
         4          0          0          0          0       1001   ch378_hal.o
       410         32          0          0          0       4682   ch378_spi_hw.o
       322          0          0          0          0       6259   ch395cmd.o
       382         32          0          0          0       4411   ch395spi.o
       404         34          0          0          0      16296   file_sys.o
         8          0          0          0          0     126743   gd32f4xx_adc.o
       264          0          0          0          0       3662   gd32f4xx_can.o
       204          4          0          0          0       4370   gd32f4xx_exmc.o
        48         16          0          0          0       1374   gd32f4xx_exti.o
       280         36          0          0          0       4717   gd32f4xx_fmc.o
       238          0          0          0          0       4501   gd32f4xx_gpio.o
       332         52          0          0          0      18212   gd32f4xx_it.o
        24          0          0          0          0        656   gd32f4xx_misc.o
        16          4          0          0          0        578   gd32f4xx_pmu.o
       464         50          0          0          0       6642   gd32f4xx_rcu.o
       620         60          0          0          0       9082   gd32f4xx_rtc.o
        76          0          0          0          0       3834   gd32f4xx_spi.o
       390         42          0          0          0       4313   gd32f4xx_timer.o
        58          8          0          0          0       1816   gd32f4xx_trng.o
        30          0          0          0          0       1331   gd32f4xx_usart.o
         0          0          0         17        512       3899   ins_data.o
       412         88        181          0        601       3994   logger.o
       648         84          0         32        256       9213   main.o
        36          8        428          0       1024        900   startup_gd32f450_470.o
       308         30          0          0          0       2233   system_gd32f4xx.o
       140         18          0          8          0       2865   systick.o
       318          8          0          8          0       6954   tcpserver.o
       812         56         96       1344          0       7155   time_unify.o

    ----------------------------------------------------------------------
     10356        <USER>        <GROUP>       1720       3444     281156   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          3         10          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       280         28          0          0          0        156   floor.o
        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       140          6         12          0          0         76   localtime_i.o
        12          6          0          0         44         68   localtime_w.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
       168         18         12          0          0         80   mktime.o
      2308         96          0          0          0        604   printfa.o
         0          0          0          4          0          0   stdout.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      4704        <USER>         <GROUP>          4         48       2764   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       280         28          0          0          0        156   m_wm.l
      3144        142         24          4         44       1584   mc_w.l
      1274          0          0          0          0       1024   mf_w.l

    ----------------------------------------------------------------------
      4704        <USER>         <GROUP>          4         48       2764   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15060       1088        764       1724       3492     268248   Grand Totals
     15060       1088        764        292       3492     268248   ELF Image Totals (compressed)
     15060       1088        764        292          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15824 (  15.45kB)
    Total RW  Size (RW Data + ZI Data)              5216 (   5.09kB)
    Total ROM Size (Code + RO Data + RW Data)      16116 (  15.74kB)

==============================================================================

