#ifndef _DATASTRUCT_H
#define _DATASTRUCT_H
/*****************************************************�ļs˵��******************************************************************************/
/*�ļs���ƣ�DATASTRUCT.h                                                                                                                   */
/*�汾�ţ�  Ver 0.1                                                                                                                        */
/*��G�����/ʱ�䣺                                                                                                */
/*��G��ˣ�                                                                                                                             */
/*�����ļs��TYPEDEFINE.h                                                                                                                   */
/*�������i����GNSSlocusGen.m�ļs�����������sϵͳ�������i������ģ��������i�Iȱ                                                              */
/*˵�������ļs�����˳�G��G��G���i�I�ṹ�������ļsΪ��ʼ���԰汾���ļs�G�������i�I�ṹ������G������Ա�ο�ѡ�ã�                           */
/*******************************************************************************************************************************************/
#include "CONST.h"
#include "TYPEDEFINE.h"
/*******************************************************ϵͳ�Լ�ṹ�嶨��******************************************************************/
typedef struct s_SelfTest
{
    DPARA LastGyroRaw[3];                       //??????,??:????????

    DPARA LastAccRaw[3];                        //????????,??:????????

    DPARA GyroRaw[3];                       //??????,??:????????

    DPARA AccRaw[3];                        //????????,??:????????

    UINT8 IMU_Valid;

    COUNT GyroRepeat_Count[3]; //����ԭʼ������ֵ�ظ�

    COUNT AccRepeat_Count[3];//�ӱ�ԭʼ������ֵ�ظ�
}SelfTest,*p_SelfTest;





typedef struct s_IMUSmoothAverage
{
    DPARA GyroBuffer[IMU_SMOOTH_AVE_NUM][3];

    DPARA AccBuffer[IMU_SMOOTH_AVE_NUM][3];

    DPARA GyroSmoothSum[3];

    DPARA AccSmoothSum[3];

    DPARA GyroSmoothMean[3];

    DPARA AccSmoothMean[3];

    DPARA GyroSmoothSum_200Hz[3];

    DPARA AccSmoothSum_200Hz[3];

    DPARA GyroSmoothMean_200Hz[3];

    DPARA AccSmoothMean_200Hz[3];

    DPARA GyroSmoothSum_100Hz[3];

    DPARA AccSmoothSum_100Hz[3];

    DPARA GyroSmoothMean_100Hz[3];

    DPARA AccSmoothMean_100Hz[3];

    DPARA Temp;

    COUNT Smooth_Count;

    COUNT Smooth_200Hz_Count;

    COUNT Smooth_100Hz_Count;

    BOOL isSmoothBufferFull;

    DPARA r_VG_Pitch;
    DPARA r_VG_Roll;
    ACCELER Total_Acc;
    TIME SmoothTime;
    DPARA Cnb[9];
    DPARA Q[4];
    BOOL isVGInit;
    DPARA Err_I[3];
}IMUSmoothAverage,*p_IMUSmoothAverage;




/*******************************************************ϵͳ�����ṹ�嶨��******************************************************************/
typedef struct s_SysVar
{
	PHASE WorkPhase;           //ϵͳ�����׶Σ��䶨����μ�CONST.h
	
	TIME Time;                 //ϵͳ����ʱ�䣬�䶨����μ�CONST.h����λ��s
	
	TIME Time_INS_Alone;  //���߹���ʱ��
	
	TIME Time_Integrated_Navi;//��Ϲ���ʱ��
	
	TIME Time_Since_Last_GNSS;
		
	LEN Arm_GNSSToINS_b[3];//bϵ�µ�GNSS��ߵ��˱ۣ�����ǰ���ҵ���ϵ���ã��Թߵ�Ϊ��������
	
	LEN Arm_GNSSToINS_n[3];//nϵ�µ�GNSS��ߵ��˱ۣ����ձ��춫����ϵ���ã��Թߵ�Ϊ��������
	
	LEN Arm_INSToCenter_b[3];//bϵ�µĹߵ�������������ĸ˱ۣ�����ǰ���ҵ���ϵ���ã��������������Ϊ��������
	
	LEN Arm_INSToCenter_n[3];//nϵ�µĹߵ�������������ĸ˱ۣ����ձ��춫����ϵ���ã��������������Ϊ��������
		
	BOOL isGNSS_Update;//��������
	
	BOOL isGNSSValid;//������Ч
	
	BOOL isPPSStart;//PPS��ʼ
	
	BOOL isFineAlign;
	
	BOOL isRapidAlign;
	
	COUNT Virtual_PPS_Count;
		
	BOOL isDampedOK;
	
	BOOL isVGInit;
	
	TIME Time_FineAlign;
	
	COUNT Valid_PPS_Count;
	
	DPARA Xk_M[DIM_STATE];
	
	DPARA Pk_M[DIM_STATE];
}SysVar,*p_SysVar;




/***************************************************�ߵ���ʼװ���ṹ�嶨��******************************************************************/
typedef struct s_InitBind
{
	LATI r_InitLati;             //���ȱ�ʾ�ĳ�ʼװ���ߵ�γ�ȣ���λ��rad��������-�G/2 ~ +�G/2,���Ϊ0����γΪ�i����γΪ��
  
  LOGI r_InitLogi;             //���ȱ�ʾ�ĳ�ʼװ���ߵ����ȣ���λ��rad��������-�G ~ +�G������������Ϊ0������Ϊ�i������Ϊ��
  
  HEIGHT InitHeight;              //��ʼװ���ߵ����θ߶ȣ���λ�� m
	
	
	VEL InitVn[3];             //��ʼװ���ߵ�����ϵ�ٶȣ���λ��m/s���i���i��Ԫ�ض������£�InitVn[0]��ʾ�ߵ�������ٶȣ�InitVn[1]��ʾ�ߵ�������ٶȣ�InitVn[2]��ʾ�ߵ�������ٶ�
	
	ATTI r_InitHead;
	
	BOOL isBind;
	
	BOOL isHeadBind;
	
}InitBind,*p_InitBind;

/***************************************************�ߵ���ʼ��׼�ṹ�嶨��******************************************************************/
typedef struct s_Align
{	
  MATR AlignCnb[9];          //�ߵ���ʼ��׼��̬���󣺵�λ���S���þ����G�Ϊ��λ�i������
  
  QUAT AlignQ[4];            //�ߵ���ʼ��׼��̬��Ԫ�i����λ���S������̬��Ԫ�i��G�Ϊ��һ����λ��Ԫ�i
  
  ACCELER AccSum[3];
  
  ANGRATE GyroSum[3];
  
  ATTI r_AlignAtti[3];       //�Ի��ȱ�ʾ���i����̬���(˳G�Ϊ���򡢸��������)����λ��rad
  
  COUNT AlignCount;          //��׼�ۻi���i
	
	//TIME AlignTime;            //��׼����ʱ���ۼ�
    	
}Align,*p_Align;


/***************************************************��G��i������ϵ��ʼ��׼�ṹ�嶨��******************************************************************/
typedef struct s_InertialSysAlign
{
    //���I�����ٶȼ�������i�I
    ANGRATE r_Wibb[2][3];           //��������ڹ�GԿռ���i������ʣ��i��ֱgָ�������ǰ���ϡ��ң����Ÿ��I���ֶ���ȷ��������λ��rad/s

    ACCELER Fibb[3];                //��������ڹ�GԿռ���i��������i��ֱgָ�������ǰ���ϡ��ң���λ��m/s2

    DELANG r_DelSenbb_1[3];

    DELANG r_DelSenbb_2[3];

    DELANG r_DelSenbb[3];

    LATI r_Lati;               //���ȱ�ʾ�Ĺߵ�γ�ȣ���λ��rad��������-�G/2 ~ +�G/2,���Ϊ0����γΪ�i����γΪ��

    LOGI r_Logi;               //���ȱ�ʾ�Ĺߵ����ȣ���λ��rad��������-�G ~ +�G������������Ϊ0������Ϊ�i������Ϊ��

    HEIGHT Height;             //�ߵ����θ߶ȣ���λ�� m

    MATR Cbib0[9];             //����ϵbϵ����ʼʱ�������G��i������ϵib0�ķ������Ҿ���

    MATR Cib0b[9];             //��ʼʱ�������G��i������ϵib0������ϵbϵ�ķ������Ҿ���

    MATR Cie[9];               //��G�ϵiϵ����������ϵeϵ�ķ������Ҿ���

    MATR Cen[9];               //��������ϵe������ϵn�ķ������Ҿ��󣬵���ϵΪ���춫����

    MATR Cib0i[9];             //��ʼʱ�̹�G��i������ϵ����G�ϵi�ķ������Ҿ���

    QUAT Qbib0[4];             //bϵ��ib0ϵ����̬��Ԫ�i

    VEL Vi[3];                 //iϵ�±����i��ֵ

    VEL Vib0[3];               //ib0ϵ�±����i��ֵ

    VEL Vi_T1[3];              //��¼t1ʱ��iϵ�±����i��ֵ

    VEL Vib0_T1[3];            //��¼t1ʱ��ib0ϵ�±����i��ֵ

    ACCELER Gn;                //�����������ٶȣ���λ��m/s2

    MATR AlignCnb[9];          //�ߵ���ʼ��׼��̬���󣺵�λ���S���þ����G�Ϊ��λ�i������

    QUAT AlignQ[4];            //�ߵ���ʼ��׼��̬��Ԫ�i����λ���S������̬��Ԫ�i��G�Ϊ��һ����λ��Ԫ�i

    ATTI r_AlignAtti[3];       //�Ի��ȱ�ʾ���i����̬���(˳G�Ϊ���򡢸��������)����λ��rad

    BOOL isT1Record;

    TIME T1;                   //��¼�G��ʱ���t1���˴�û�G��ȷ��ʾt2�����ʱ�̼�Ϊt2

    TIME AlignTime;            //��׼ʱ���¼����λ:s

    COUNT AlignCount;          //��׼�ۻi���i   

    BOOL isAlign_Finish;       //��׼��ɱ�־
}InertialSysAlign,*p_InertialSysAlign;





/***************************************************������������ϵ��ʼ��׼�ṹ�嶨��******************************************************************/
typedef struct s_DynamicInertialSysAlign
{	
  //���ݡ����ٶȼ����������
  ANGRATE r_Wibb[2][3];              //��������ڹ��Կռ����������ʣ�����ֱ�ָ�������ǰ���ϡ��ң����Ÿ������ֶ���ȷ��������λ��rad/s
	
	DELANG r_DelSenbb_1[3];
	
	DELANG r_DelSenbb_2[3];
	
	DELANG r_DelSenbb[3];
  
  ACCELER Fibb[3];                //��������ڹ��Կռ���������������ֱ�ָ�������ǰ���ϡ��ң���λ��m/s2
  
  LATI r_Lati;               //���ȱ�ʾ�Ĺߵ�γ�ȣ���λ��rad��������-��/2 ~ +��/2,���Ϊ0����γΪ������γΪ��
  
  LOGI r_Logi;               //���ȱ�ʾ�Ĺߵ����ȣ���λ��rad��������-�� ~ +�У�����������Ϊ0������Ϊ��������Ϊ��
  
  HEIGHT Height;             //�ߵ����θ߶ȣ���λ�� m

  VEL Vn[3];

  VEL LastVn[3];

  LEN Rm;                    //��������Ȧ�뾶����λ��m

  LEN Rn;                    //����î��Ȧ�뾶����λ��m

  DPARA invRm;               //��������Ȧ�뾶�ĵ�������λ��1/m

  DPARA invRn;               //����î��Ȧ�뾶�ĵ�������λ��1/m

  ANGRATE r_Wien[3];         //���ȱ�ʾ�ĵ�����ת������Wie�ڵ�������ϵ�����µķ�������λ��rad/s

  ANGRATE r_Wenn[3];         //���ȱ�ʾ�ĵ���ϵ��Ե�����ת������Wen�ڵ�������ϵ�����µķ�������λ��rad/s

  //ANGRATE r_Wnbb[3];         //���ȱ�ʾ������ϵ����ڵ���ϵ����ת������������ϵ�����µķ���������ֱ�ָ��ǰ���ϡ��ң���λ��rad/s

  MATR Cbib0[9];             //����ϵbϵ����ʼʱ�����������������ϵib0�ķ������Ҿ���
  
  MATR Cib0b[9];             //��ʼʱ�����������������ϵib0������ϵbϵ�ķ������Ҿ���
  
  MATR Cie[9];               //����ϵiϵ����������ϵeϵ�ķ������Ҿ���
  
  MATR Cen[9];               //��������ϵe������ϵn�ķ������Ҿ��󣬵���ϵΪ���춫����
  
  MATR Cib0i[9];             //��ʼʱ�̹�����������ϵ������ϵi�ķ������Ҿ���
  
  QUAT Qbib0[4];             //bϵ��ib0ϵ����̬��Ԫ��
  
  VEL Vi[3];                 //iϵ�±�������ֵ

  VEL Vib0[3];               //ib0ϵ�±�������ֵ
  
  VEL Si_T1[3];              //��¼t1ʱ��iϵ�±�������ֵ

  VEL Sib0_T1[3];            //��¼t1ʱ��ib0ϵ�±�������ֵ

  LEN Si[3];

  LEN Sib0[3];
  
  ACCELER Gn;                //�����������ٶȣ���λ��m/s2
  
  MATR AlignCnb[9];          //�ߵ���ʼ��׼��̬���󣺵�λ���ޣ��þ������Ϊ��λ��������
  
  QUAT AlignQ[4];            //�ߵ���ʼ��׼��̬��Ԫ������λ���ޣ�����̬��Ԫ������Ϊ��һ����λ��Ԫ��
  
  ATTI r_AlignAtti[3];       //�Ի��ȱ�ʾ��������̬���(˳��Ϊ���򡢸��������)����λ��rad
  
  BOOL isT1Record;
  
  TIME T1;                   //��¼�м�ʱ���t1���˴�û����ȷ��ʾt2�����ʱ�̼�Ϊt2
  
  TIME AlignTime;            //��׼ʱ���¼����λ:s
  
  COUNT AlignCount;          //��׼�ۻ�����

  BOOL isAlignInit;
	
	BOOL isAlign_Finish;       //��׼��ɱ�־
    	
}DynamicInertialSysAlign,*p_DynamicInertialSysAlign;





/*****************************************************��GԵ����ṹ�嶨��********************************************************************/
typedef struct s_Navi
{            
    //���I�����ٶȼ�������i�I
    ANGRATE r_Wibb[2][3];              //��������ڹ�GԿռ���i������ʣ��i��ֱgָ�������ǰ���ϡ��ң����Ÿ��I���ֶ���ȷ��������λ��rad/s
    ACCELER Fibb[2][3];                //��������ڹ�GԿռ���i��������i��ֱgָ�������ǰ���ϡ��ң���λ��m/s2

    //�ߵ��ڲ�������i�I��������λ�������

    ATTI r_Atti[3];            //���ȱ�ʾ�Ĺߵ���̬�ǣ��i�������ֱg��ʾ����ǡ������ǡ�����ǣ���λ��rad

    MATR Cnb[9];               //�ߵ���̬���󣬵�λ���S���þ����G�Ϊ��λ�i������

    MATR Cbn[9];

    QUAT Q[4];                 //�ߵ���̬��Ԫ�i����λ���S����������̬��Ԫ�i��G�Ϊ��һ����Ԫ�i

    LEN Rm;                    //��������Ȧ�뾶����λ��m

    LEN Rn;                    //����î��Ȧ�뾶����λ��m

    DPARA invRm;               //��������Ȧ�뾶�ĵ��i����λ��1/m

    DPARA invRn;               //����î��Ȧ�뾶�ĵ��i����λ��1/m

    ANGRATE r_Wien[3];         //���ȱ�ʾ�ĵ�����ת������Wie�ڵ�������ϵ�i���µķ�������λ��rad/s

    ANGRATE r_Wenn[3];         //���ȱ�ʾ�ĵ���ϵ��Ե���Giת������Wen�ڵ�������ϵ�i���µķ�������λ��rad/s

    ANGRATE r_Wnbb[2][3];         //���ȱ�ʾ������ϵ����ڵ���ϵ��Giת������������ϵ�i���µķ������i��ֱgָ��ǰ���ϡ��ң���λ��rad/s

    DELANG r_DelSenbb_1[3];

    DELANG r_DelSenbb_2[3];

    DELANG r_DelSenbb[3];

    ACCELER Vibn[3];           //�ߵ��i����ٶȼ���������������궨���i��)�ڵ���ϵ�µ�ͶӰ����λ��m/s2,�i��ֱgָ�򵼺�ϵ�ı����졢���i������

    ACCELER Fibn[3];

    ACCELER Aenn[3];           //�ߵ��ڵ���ϵ�G�ļ��ٶȣ���λ��m/s2,�i��ֱgָ�򵼺�ϵ�ı����졢���i������

    ACCELER Gn;                //�����������ٶȣ���λ��m/s2                

    LATI r_Lati;               //���ȱ�ʾ�Ĺߵ�γ�ȣ���λ��rad��������-�G/2 ~ +�G/2,���Ϊ0����γΪ�i����γΪ��

    LOGI r_Logi;               //���ȱ�ʾ�Ĺߵ����ȣ���λ��rad��������-�G ~ +�G������������Ϊ0������Ϊ�i������Ϊ��

    LATI r_Lati_Err;               

    LOGI r_Logi_Err; 
		
		HEIGHT Height_Err;

    DPARA K_Correct_Lati;

    DPARA K_Correct_Logi;
		
		DPARA K_Correct_Height;

    DPARA VnErr[3];

    DPARA r_AttiErr_n[3];

    COUNT Correct_Count;

    ANGRATE r_AttiRate[3];     //���ȱ�ʾ�Ĺߵ���̬�����ʣ��i�������ֱg��ʾ��������ʡ����������ʡ���������ʣ���λ��rad/s

    //���I�����ٶȼ���ƫGS�i��
    ANGRATE r_GyroBias[3];

    ACCELER AccBias[3];
    //�ߵ�ʵʱ���㡢������i�I������λ�������
    COUNT Navi_Count;          //�ߵ����i����������ͳ�ƹߵ�����Ĵ��i����λ����

    ATTI d_Atti[3];            //Բ�ȱ�ʾ�Ĺߵ���̬�ǣ��i�������ֱg��ʾ��������ʡ����������ʡ����������,��λ����/s��

    ANGRATE d_AttiRate[3];     //Բ�ȱ�ʾ�Ĺߵ���������ʣ���λ����/s

    LATI d_Lati;               //Բ�ȱ�ʾ�Ĺߵ�γ�ȣ���λ���㣬������-90�� ~ +90��,���Ϊ0����γΪ�i����γΪ��

    LOGI d_Logi;               //Բ�ȱ�ʾ�Ĺߵ����ȣ���λ���㣬������-180�� ~ +180�㣬����������Ϊ0������Ϊ�i������Ϊ��

    VEL LastVn[3];              //�ߵ��ڵ���ϵ�Gǰһ�������ڵ��ٶȣ���λ��m/s���i���i��Ԫ�ض������£�Vn[0]��ʾ�ߵ������ٶȣ�Vn[1]��ʾ�ߵ������ٶȣ�Vn[2]��ʾ�ߵ������ٶ�

    VEL Vn[3];                  //�ߵ��ڵ���ϵ�G���ٶȣ���λ��m/s���i���i��Ԫ�ض������£�Vn[0]��ʾ�ߵ������ٶȣ�Vn[1]��ʾ�ߵ������ٶȣ�Vn[2]��ʾ�ߵ������ٶ�

    //VEL Vb[3];                  //�ߵ�������ϵ�G���ٶȣ���λ��m/s, �i���i��Ԫ�ض������£�Vb[0]��ʾ�ߵ�ǰ���ٶȣ�Vb[1]��ʾ�ߵ������ٶȣ�Vb[2]��ʾ�ߵ������ٶ�

    LEN Sn[3];                    //�ߵ��ڵ���ϵ�G��λ�ƣ���λ��m���i���i��Ԫ�ض������£�Sn[0]��ʾ�ߵ�����λ�ƣ�Sn[1]��ʾ�ߵ�����λ�ƣ�Sn[2]��ʾ�ߵ�����λ��

    HEIGHT Height;                //�ߵ����θ߶ȣ���λ�� m

    HEIGHT DampHeight;

    BOOL isDampHeight;

    BOOL isHeadBind;

    DPARA K3_Integral;

    BOOL isHeadingchange;

    BOOL isHeadingChangeLarge;

    LEN GNSSLeverArm[3];                  //GPS�˱۳���

    VEL Vn_r[3];                         //nϵ�±�ʾ�ĸ˱��ٶ�

    ANGRATE d_HeadingRateBuffer[HEADINGRATE_BUFFER_SIZE];          //��������ʣ�����ƽ������

    ANGRATE d_HeadingRate_Mean;

    COUNT HeadingRate_Circle_Count;

    MATR Cpb[9];

    VEL Vp[3];

    VEL VnCorrBuffer[3];

    //ANGRATE r_Gyro_Bias[3];

    //ACCELER Acc_Bias[3];

    VEL DampedV[3];

    ACCELER Horizontal_Acc;

    COUNT VnBufferCount;

} Navi,*p_Navi;


// typedef struct {
//     MATR Fk[DIM_STATE * DIM_STATE];
// } HistoryFk;  // 单个时刻的数据

/*****************************************************Kalman�˲��ṹ�嶨��********************************************************************/
typedef struct s_Kalman
{
	MODE Obv_Fit_Mode;
	
	MODE Work_Mode;
	
	BOOL isInsRecord;                    //�Ƿ��¼��ǰʱ��GNSS�i�I
	
	BOOL isKalmanStart;                  //�˲���ʼGź�
	
	BOOL isKalmanComputeStart;           //�˲�������㿪ʼGź�
	
	BOOL isCorrectError;                 //���G��iGź�
	
	BOOL isVnCorrect;                    //�Ƿ�GS�i�ٶ����
	
	BOOL isPosCorrect;                   //�Ƿ�GS�iλ�����
	
	BOOL isAttiCorrect;                  //�Ƿ�GS�i��̬���
	
	BOOL isHeadingCorrect;               //�Ƿ�GS�i�������
	
	BOOL isGyroDriftCorrect;             //�Ƿ�GS�i���I��ƫ���
	
	BOOL isAccDriftCorrect;              //�Ƿ�GS�i���ٶȼ���ƫ���
	
	IPARA Actual_Dim_Obv;               //ʵ�ʹ۲���ά�i
	
	VEC Zk[DIM_MAX_OBV];                 // ������Ϊ�G������ά�i��DIM_MAX_OBV����λ�����I���G�������������ȷ��
	
	MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV];  //��������G��������ά�i��DIM_MAX_OBV * DIM_MAX_OBV
	
	VEC Xk[DIM_STATE];                   //���״̬����������Ϊ�G������ά�i:DIM_STATE����λ�����I���G��״̬��������ȷ��
	
	VEC Xkk_1[DIM_STATE];                //һ��Ԥ�����״̬����������Ϊ�G������ά�i:DIM_STATE����λ�����I���G��״̬��������ȷ��
	
	MATR Hk[DIM_MAX_OBV * DIM_STATE];    //�������ά�i: DIM_MAX_OBV * DIM_STATE
	
	MATR Fn[DIM_STATE * DIM_STATE];      //һ��״̬ת�ƾ����ÿ�����������ۼӲ���,ά�i��DIM_STATE * DIM_STATE
	
	MATR Fk[DIM_STATE * DIM_STATE];      //һ��״̬ת�ƾ���ά�i��DIM_STATE * DIM_STATE
	
	MATR Kk[DIM_STATE * DIM_MAX_OBV];    //Ԥ�����GS�i����ά�i��DIM_STATE * DIM_MAX_OBV
	
  MATR Pk[DIM_STATE * DIM_STATE];      //״̬���G��������ά�i��DIM_STATE * DIM_STATE
	
  MATR Pkk_1[DIM_STATE * DIM_STATE];   //һ��Ԥ��״̬���G��������ά�i��DIM_STATE * DIM_STATE
	
  MATR Qk[DIM_STATE * DIM_STATE];      //ϵͳ����G��������ά�i��DIM_STATE * DIM_STATE
	
	MATR TrFk[DIM_STATE * DIM_STATE];
	
	MATR Fk_Pk[DIM_STATE * DIM_STATE];
	
  VEC InsVn[3];                        //�ߵ��ٶȣ���λ��m/s
  
  LEN GPSLeverArm[3];                  //GPS�˱۳���
  
  VEL Vn_r[3];                         //nϵ�±�ʾ�ĸ˱��ٶ�
  
  ATTI r_InsFai;
	
	LATI r_InsLati; 
	
	LOGI r_InsLogi;
	
	HEIGHT InsHeight;
	
	ACCELER Acc_Horizontal;
	
	ACCELER Acc_All;
	
	//COUNT HeadingValidCount;
	
	COUNT Kal_Count;                     //Kalman�˲����i��������λ����
	
	COUNT ComputeFn_Count;
	
	COUNT Kal_Predict_Count;
	
	//BOOL isZeroSpeedInsRecord;
	
	//BOOL isDRInsRecord;
	
	BOOL isInsPosRecord;
	
	BOOL isHeightRecord;
	
	COUNT State;

  VEC History_InsPos[3];

  LEN delay_time;

  COUNT history_pos_count;

  COUNT history_pos_delay_count;
	
} Kalman, *p_Kalman;

/*****************************************************GNSS�i�I�ṹ�嶨��********************************************************************/
typedef struct s_GNSSData 
{
    DPARA Hdop;                          //GNSS�i�I��GDOPֵ
	
	  DPARA Vdop;

    LATI r_GNSSLati;                     //GNSSγ�ȣ���λ��rad

    LOGI r_GNSSLogi;                     //GNSS���ȣ���λ��rad

    HEIGHT GNSSHeight;                   //GNSS�߶ȣ���λ��m

    //HEIGHT GNSSDamped_Height;

    ATTI r_GPSHead;	//˫���ߺ���

    ATTI r_TrackAtti;//GNSS������

    VEL GNSSVn[3];     //��������ϵ���i��GNSS�ٶȣ��i��ֱgΪGNSS���١�GNSS���١�GNSS���٣���λ��m/s

    VEL GNSS_V;//ˮƽ�ٶ�

    //VEL MAX_GNSS_V;
	
	  COUNT UseSatNum;

    BOOL GNSS_State;
		
		INT8 GNSS_POS_State;
		
		UINT16 GNSS_V_State;
		
		UINT8 GNSS_Head_State;

    BOOL isVelEn;

    BOOL isPosEn;

    BOOL isHeadingEn;

    BOOL isHeightEn; 

    COUNT GNSS_Valid_Count;
		
		BOOL isGNSS_Valid_3s;

    //BOOL isGNSS_V_Static;
}GNSSData,*p_GNSSData;


/*****************************************************���I���ٶȼƼ��¶ȴ��G��ԭʼ�i�I��ȡ�ṹ�嶨��********************************************************************/
typedef struct s_SenorRaw
{
		INT32 GyroRaw[3];
		
		INT32 GyroTempRaw[3];
		
		INT32 AccRaw[3];
		
		INT32 AccTempRaw[3];
		
		INT32 BoardtempRaw;//��·���¶�		
}SenorRaw,*p_SenorRaw;
/*****************************************************???????????********************************************************************/
typedef struct s_Compen
{
	//?????????????????
	DPARA GyroRaw[3];                       //??????,??:????????
	DPARA AccRaw[3];                        //????????,??:????????
	
	DRAW RTGyroBias[3];
	
	MATR RTGyroScalFac[3];
	
	IPARA GyroTempRangeNum[3];
	
	
	MATR AccLeverArm[9];  //???????????????,??:m 
	
	DRAW RTAccBias[3];
	
	MATR RTAccScalFac[3];
	
	//MATR RTAccFacNonLiner[3];
	
	IPARA AccTempRangeNum[3];
	          
	//??????????????????
	DPARA Gyro[3];                            //???????,??:????????:rad?rad/s,�/s,�?
	DPARA Acc[3];                             //?????????,??:????????:g?m/s2?
	
	DPARA LastGyro[3];                            
	DPARA LastAcc[3];
	
	DPARA GyroTemp[3];                        //?????????,??:�C
	DPARA AccTemp[3];    	//???????????,??:�C
	
	//????????
	DPARA GyroTemp_1s_Sum[3];
	DPARA GyroTemp_1s_Mean[3];
	DPARA AccTemp_1s_Sum[3];
	DPARA AccTemp_1s_Mean[3];
	COUNT GyroCount_1s;
	COUNT AccCount_1s;

	//
	DPARA GyroTemp_Move_Mean_Buffer[3][NUM_TEMP_DIFF_BUFFER];
	DPARA AccTemp_Move_Mean_Buffer[3][NUM_TEMP_DIFF_BUFFER];

	DPARA GyroTemp_Diff[3];
	DPARA AccTemp_Diff[3];

	COUNT AccCircle_Count;
	COUNT GyroCircle_Count;
	BOOL isGyroMove_Mean_BufferInit;
	BOOL isAccMove_Mean_BufferInit;

	DPARA Boardtemp;
	
	COUNT Count_GyroErr[3];
	
	COUNT Count_AccErr[3];
	
	//BOOL isIMUFail;	
	
	UINT8 IMU_Valid;
	
	COUNT IMU_Count;
}Compen,*p_Compen;

/*****************************************************?????????????????********************************************************************/
typedef struct s_ANNCompen
{
	//?????????
	DPARA Dense1_Bias[DENSE_1_CELL_NUM];
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM];
	
	DPARA Dense2_Bias[DENSE_2_CELL_NUM];
	DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM];
	
	DPARA Dense3_Bias;
	DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM];
	
	
	DPARA Normalized_Temp_Max;
	DPARA Normalized_Temp_Diff_Max;
	DPARA Normalized_Output_Max;
	
	DPARA Normalized_Temp_Min;
	DPARA Normalized_Temp_Diff_Min;
	DPARA Normalized_Output_Min;
	
	DPARA Normalized_Temp_Mean;
	DPARA Normalized_Temp_Diff_Mean;
	DPARA Normalized_Output_Mean;
	//??????
	DPARA Correct_Value;
}ANNCompen,*p_ANNCompen;
#endif
