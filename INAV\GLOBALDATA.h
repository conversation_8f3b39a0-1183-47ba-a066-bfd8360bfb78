#ifndef _GLOBALDATA_H
#define _GLOBALDATA_H
/*****************************************************文件说明******************************************************************************/
/*文件名称：GLOBALDATA.h                                                                                                                   */
/*版本号：  Ver 0.1                                                                                                                        */
/*编写日期/时间：                                                                                                */
/*编写人：                                                                                                                             */
/*包含文件：无                                                                                                                             */
/*测试用例：由GNSSlocusGen.m文件生成整套软件系统测试用例，单个模块测试用例暂缺                                                              */
/*说明：本文件定义了程序中所需的全局变量。（此文件为初始测试版本，文件中所定义全局常量仅供程序设计人员参考选用）                       */
/*******************************************************************************************************************************************/
#include "DATASTRUCT.h"
//#include "data.h"
//结构体全局变量
SelfTest g_SelfTest;//声明系统自检结构体全局变量

SysVar g_SysVar;  //声明系统变量结构体全局变量

InitBind g_InitBind;//声明惯导初始装订结构体全局变量

Align g_Align;//声明惯导初始对准结构体全局变量

Navi g_Navi;//声明导航结构体全局变量

Kalman g_Kalman;//声明Kalman滤波结构体全局变量

GNSSData g_GNSSData_In_Use,g_GNSSData_For_FineAlign;//声明GNSS数据结构体全局变量

Compen g_Compen;//声明补偿参数结构体全局变量

InertialSysAlign g_InertialSysAlign;//声明惯性凝固坐标系初始对准结构体全局变量

DynamicInertialSysAlign g_DynamicInertialSysAlign;

IMUSmoothAverage g_IMUSmoothAverage;
#endif
