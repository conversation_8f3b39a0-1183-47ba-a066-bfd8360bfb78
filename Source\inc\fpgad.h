/*!
    \file  main.h
    \brief the header file of main 
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef __FPGAD_H
#define __FPGAD_H
#include "gd32f4xx_can.h"
//#include "frame_analysis.h"
#include "appdefine.h"


#pragma pack(1)
//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct _fpgadata {
	unsigned short	DATA_LEN;	//1
	unsigned short	NUM_CLK;	//2
	unsigned short	VERSION;	//3

	short	UNOtemperature;		//4
	short	DUEtemperature;		//5
	short	rateX_data;			//6
	short	rateY_data;			//7
	short	rateZ_data;			//8
	short	accX_data;			//9
	short	accY_data;			//10
	short	accZ_data;			//11

	short	magGrp0;			//12
	short	magGrp1;			//13
	short	magGrp2;			//14

	int	fog_x;					//15
	int fog_y;					//17
	int fog_z;					//19
	unsigned short	fog_tempx;	//18
	unsigned short	fog_tempy;	//19
	unsigned short	fog_tempz;	//20
	int axis3_accx;			//21
	int axis3_accy;			//22
	int axis3_accz;			//23
	unsigned short	axis3_temp;	//24

	unsigned short	reserved[3];		//25 ~ 27

	unsigned short	hGPSData_gpsweek;	//28
	unsigned int	hGPSData_gpssecond;	//29
	unsigned int	gpssecond982;		//30
	unsigned int	ppstimesdelay;		//31

	unsigned short	GPGGA_STAR;			//32
	unsigned short	rtkstatus;			//33
	unsigned short	gnssspeedstatus;	//34
	unsigned short	GPRMC_TRA[3];		//35


	float	hGPSData_vn;			//36
	float	hGPSData_ve;			//37
	float	hGPSData_vu;			//38
	unsigned short	gnsspositionstaus;		//39

	unsigned short directionofLat;	//40
	double	hGPSData_Lat;			//41
	unsigned short directionofLon;	//42
	double	hGPSData_Lon;			//43
	double	hGPSData_Alt;			//44

	unsigned short headingstate;	//45
	float baselinelength;			//46

	float	hGPSData_Roll;			//47
	float	hGPSData_Pitch;			//48
	float	hGPSData_Yaw;			//49

#if 1	//10/10 append
	float ECEF_X;		//50
	float ECEF_Y;		//51
	float ECEF_Z;		//52
	float geometry_z;	//53
	float location_z;	//54
	float time_z;		//55
	float vertical_z;	//56
	float horizontal_z;	//57
	float north_z;		//58
	float east_z;		//59
	float endheight_z;	//60
#endif
	unsigned short	checksum;		//61
	unsigned short	checksumgd;		//62
	unsigned short	packetindex;	//63
	
	//The following are the results of the algorithm
	double	Alongitude;	//算法结果，经纬高
	double	Alatitude;	//算法结果，经纬高
	float	Aaltitude;	//算法结果，经纬高
	float	Ave;		//算法结果，东向速度
	float	Avn;		//算法结果，北向速度
	float	Avu;		//算法结果，天向速度
	float	Apitch;		//算法结果，俯仰角
	float	Aroll;		//算法结果，横滚角
	float	Aheading;	//算法结果，偏航角
	unsigned short	checksumA;		//62
} fpgadata_t;

typedef struct
{
	unsigned short	head1;			//??BB11
	unsigned short	head2;			//??DBBD
	unsigned short  dataLen;		//??????????????????????????????У??
    fpgadata_t fpgadata;			//FPGA???????
    unsigned int fpgaItrCount;		//FPGA?ж????
    unsigned int fpgaLoopCount;		//FPGA???????????
	unsigned short Status;			//?????????
	unsigned short CheckSum;		//????
} FpgadataSend_t;

typedef struct
{
    float timestamp;								
	float WheelSpeed_Front_Left;		
    float WheelSpeed_Back_Left;			
    float WheelSpeed_Front_Right;		
    float WheelSpeed_Back_Right;		
    float WheelSteer;								
    float OdoPulse_1;								
    float OdoPulse_2;								
    unsigned char Gear;							

} CanData_t;

typedef struct
{
    CanData_t data;
    unsigned int counter;
	unsigned char flag;
} CanDataTypeDef;
#pragma pack()


extern	fpgadata_t		gpagedata;
extern	CanDataTypeDef	gcanInfo;

extern FpgadataSend_t gfpgadataSend;
											//????FPGA??????predo


extern unsigned short adlxdata[12];


#define	U4RX_MAXCOUNT		(1024 * 40)	//(1024 * 4)
#define	FRAMEPARSEBUFSIZE	(512 * 2)
extern	unsigned char grxbuffer[U4RX_MAXCOUNT];
extern	int grxlen, grxst;
extern	unsigned char gframeParsebuf[FRAMEPARSEBUFSIZE];
extern	can_receive_message_struct gCanRxBuf;
extern	void analysisRxdata(void);



//extern	void printf_uart4(int type, char *fmt, ...);
//extern	void uart4sendmsg(char *txbuf, int size);
//extern	void uart4sendmsg_billdebug(char *txbuf, int size);
//extern	void uart4sendmsg_canout(can_receive_message_struct *receive_message);
//extern	void analysisRxdata(void);

#endif /* __FPGAD_H */


